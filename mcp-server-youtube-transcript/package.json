{"name": "@kimtaeyoon83/mcp-server-youtube-transcript", "version": "0.1.1", "description": "This is an MCP server that allows you to directly download transcripts of YouTube videos.", "license": "MIT", "author": "<PERSON>", "homepage": "https://github.com/kimtaeyoon83/mcp-server-youtube-transcript", "bugs": "https://github.com/kimtaeyoon83/mcp-server-youtube-transcript/issues", "type": "module", "access": "public", "main": "dist/index.js", "module": "dist/index.js", "bin": {"mcp-server-youtube-transcript": "dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && shx chmod +x dist/*.js", "prepare": "npm run build", "watch": "tsc --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "0.6.0", "mcp-evals": "^1.0.18", "youtube-captions-scraper": "^2.0.3"}, "devDependencies": {"@types/node": "^20.11.24", "shx": "^0.3.4", "typescript": "^5.6.2"}}