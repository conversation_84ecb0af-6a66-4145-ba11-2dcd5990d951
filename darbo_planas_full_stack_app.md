# Full Stack Application Darbo Planas
*Pagal YouTube video: "How to build a full stack application using FastAPI and React"*

## Projekto Apžvalga
Kuriama **Coding Challenge Generator** aplikacija - full stack projektas su:
- **Backend**: FastAPI + Python
- **Frontend**: React + JavaScript  
- **Autentifikacija**: Clerk
- **Duomenų bazė**: SQLAlchemy
- **AI Integracija**: OpenAI GPT API

## 1. Projekto Struktūros Sukūrimas

### 1.1 Backend Setup
- [ ] Sukurti `backend/` katalogą
- [ ] Įdiegti UV (Python paketų tvarkytuvė)
- [ ] Inicializuoti projektą: `uv init .`
- [ ] Įdiegti priklausomybes:
  ```bash
  uv add fastapi
  uv add uvicorn
  uv add sqlalchemy
  uv add python-dotenv
  uv add clerk-sdk-python
  uv add openai
  ```
- [ ] Sukurti katalogų struktūrą:
  - `src/` - pagrindinis kodas
  - `src/database/` - duomenų bazės konfigūracija
  - `src/routes/` - API maršrutai

### 1.2 Frontend Setup
- [ ] Sukurti React projektą: `npm create vite@latest frontend -- --template react`
- [ ] Įdiegti priklausomybes:
  ```bash
  npm install react-router-dom@6
  npm install @clerk/clerk-react
  ```
- [ ] Sukurti katalogų struktūrą:
  - `src/auth/` - autentifikacijos komponentai
  - `src/challenge/` - iššūkių komponentai
  - `src/history/` - istorijos komponentai
  - `src/layout/` - bendri layout komponentai
  - `src/utils/` - pagalbiniai įrankiai

## 2. Clerk Autentifikacijos Konfigūracija

### 2.1 Clerk Projekto Sukūrimas
- [ ] Registruotis clerk.com
- [ ] Sukurti naują aplikaciją
- [ ] Konfigūruoti prisijungimo metodus (Google, email)
- [ ] Gauti Publishable Key ir Secret Key

### 2.2 Frontend Clerk Integracija
- [ ] Sukurti `.env` failą su `VITE_CLERK_PUBLISHABLE_KEY`
- [ ] Sukurti `ClerkProviderWithRoutes.jsx` komponentą
- [ ] Integruoti `ClerkProvider` su `BrowserRouter`
- [ ] Sukurti `AuthenticationPage.jsx` su:
  - `SignIn` komponentu
  - `SignUp` komponentu
  - `SignedIn` / `SignedOut` logika

### 2.3 Backend Clerk Integracija
- [ ] Konfigūruoti Clerk SDK Python'e
- [ ] Sukurti middleware token validacijai
- [ ] Implementuoti autentifikacijos dekoratorius

## 3. Komponentų Kūrimas

### 3.1 Pagrindiniai Frontend Komponentai
- [ ] `Layout.jsx` - bendras puslapio layout
- [ ] `ChallengeGenerator.jsx` - iššūkių generatorius
- [ ] `MCQChallenge.jsx` - daugiavariančių klausimų komponentas
- [ ] `HistoryPanel.jsx` - generuotų iššūkių istorija

### 3.2 Maršrutų Konfigūracija
- [ ] Sukurti React Router konfigūraciją:
  - `/` - pagrindinis puslapis (ChallengeGenerator)
  - `/sign-in/*` - prisijungimo puslapis
  - `/sign-up` - registracijos puslapis
  - `/history` - istorijos puslapis

## 4. Backend API Kūrimas

### 4.1 FastAPI Pagrindas
- [ ] Sukurti `main.py` su FastAPI app
- [ ] Konfigūruoti CORS
- [ ] Sukurti bazinį endpoint'ą
- [ ] Integruoti Uvicorn serverį

### 4.2 Duomenų Bazės Modeliai
- [ ] Sukurti SQLAlchemy modelius:
  - `User` - vartotojų duomenys
  - `Challenge` - generuoti iššūkiai
  - `UserChallengeHistory` - vartotojų istorija

### 4.3 API Endpoint'ai
- [ ] `POST /generate-challenge` - iššūkio generavimas
- [ ] `GET /user-challenges` - vartotojo iššūkių gavimas
- [ ] `GET /user-stats` - vartotojo statistikos gavimas
- [ ] `POST /submit-answer` - atsakymo pateikimas

## 5. OpenAI Integracija

### 5.1 GPT API Konfigūracija
- [ ] Gauti OpenAI API raktą
- [ ] Sukurti prompt'us iššūkių generavimui
- [ ] Implementuoti difficulty lygio logika (easy, medium, hard)

### 5.2 Iššūkių Generavimas
- [ ] Sukurti funkcijas:
  - `generate_coding_question(difficulty, topic)`
  - `generate_multiple_choices(question)`
  - `validate_answer(question, answer)`

## 6. Funkcionalumo Implementacija

### 6.1 Iššūkių Sistemos Logika
- [ ] Imeplementuoti dieninį limitą (5 iššūkiai per dieną)
- [ ] Sukurti sunkumo lygio pasirinkimą
- [ ] Generuoti atsitiktinius programavimo klausimus
- [ ] Saugoti vartotojų atsakymus ir rezultatus

### 6.2 Istorijos Funkcionalumas
- [ ] Rodyti visus ankstesnius iššūkius
- [ ] Filtruoti pagal datą ir sunkumą
- [ ] Eksportuoti rezultatus

## 7. Stilizavimas ir UX

### 7.1 CSS/Styling
- [ ] Sukurti responsive dizainą
- [ ] Implementuoti dark/light mode
- [ ] Sukurti loading state'us
- [ ] Pridėti animacijas

### 7.2 User Experience
- [ ] Pridėti error handling
- [ ] Implementuoti loading indikatorius
- [ ] Sukurti confirmation dialogs
- [ ] Optimizuoti mobile patirtį

## 8. Testavimas ir Deployment

### 8.1 Lokalus Testavimas
- [ ] Testuoti visus API endpoint'us
- [ ] Patikrinti autentifikacijos srautą
- [ ] Testuoti iššūkių generavimą
- [ ] Patikrinti responsive dizainą

### 8.2 Production Deployment
- [ ] Konfigūruoti production environment variables
- [ ] Sukurti Docker containers
- [ ] Deploy backend (Heroku/Railway/DigitalOcean)
- [ ] Deploy frontend (Vercel/Netlify)
- [ ] Sukonfigūruoti production Clerk settings

## 9. Papildomi Funkcionalumai (Plėtimas)

### 9.1 Advanced Features
- [ ] Pridėti code execution sandbox
- [ ] Implementuoti timer funkcionalumą
- [ ] Sukurti leaderboard sistemą
- [ ] Pridėti social sharing

### 9.2 Analytics ir Monitoring
- [ ] Integruoti analytics (Google Analytics)
- [ ] Sukurti admin dashboard
- [ ] Implementuoti error logging
- [ ] Pridėti performance monitoring

## Technologijų Stack'as
- **Backend**: FastAPI, Python, SQLAlchemy, Uvicorn
- **Frontend**: React, Vite, React Router DOM
- **Autentifikacija**: Clerk
- **AI**: OpenAI GPT API
- **Duomenų bazė**: SQLite (dev), PostgreSQL (prod)
- **Deployment**: Vercel (frontend), Railway/Heroku (backend)

## Ištekliai
- PyCharm IDE (rekomenduojama Python projektams)
- UV paketų tvarkytuvė (greitesnė nei pip)
- Clerk autentifikacijos dokumentacija
- OpenAI API dokumentacija
- FastAPI dokumentacija

---
*Šis planas sukurtas pagal video tutorial ir gali būti pritaikytas pagal individualius poreikius*