#!/usr/bin/env python3
"""
Test script for Claude Code Execution
Šis skriptas demonstruoja įvairias Python funkcijas
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

def analyze_sales_data():
    """Analizuoja pardavimų duomenis"""
    print("🔍 Analizuojami pardavimų duomenys...")
    
    # Skaitome duomenis
    try:
        df = pd.read_csv('../duomenys/test_data.csv')
        print(f"✅ Sėkmingai nuskaityta {len(df)} įrašų")
        
        # Pagrindinė statistika
        print("\n📊 Pagrindinė statistika:")
        print(f"Vidutiniai pardavimai: {df['sales'].mean():.2f}")
        print(f"Maksimalūs pardavimai: {df['sales'].max()}")
        print(f"Minimalūs pardavimai: {df['sales'].min()}")
        
        # Pardavimai pagal regionus
        print("\n🌍 Pardavimai pagal regionus:")
        region_sales = df.groupby('region')['sales'].sum().sort_values(ascending=False)
        for region, sales in region_sales.items():
            print(f"{region}: {sales}")
            
        return df
        
    except FileNotFoundError:
        print("❌ Failas ../duomenys/test_data.csv nerastas!")
        return None

def create_visualization(df):
    """Sukuria vizualizaciją"""
    if df is None:
        return
        
    print("\n📈 Kuriama vizualizacija...")
    
    # Sukuriame grafiką
    plt.figure(figsize=(12, 8))
    
    # Subplot 1: Pardavimai pagal regionus
    plt.subplot(2, 2, 1)
    region_sales = df.groupby('region')['sales'].sum()
    plt.bar(region_sales.index, region_sales.values, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
    plt.title('Pardavimai pagal regionus')
    plt.ylabel('Pardavimai')
    
    # Subplot 2: Pardavimai pagal produktus
    plt.subplot(2, 2, 2)
    product_sales = df.groupby('product')['sales'].sum()
    plt.pie(product_sales.values, labels=product_sales.index, autopct='%1.1f%%', 
            colors=['#FFD93D', '#6BCF7F', '#4D96FF'])
    plt.title('Pardavimų pasiskirstymas pagal produktus')
    
    # Subplot 3: Pardavimų tendencija laike
    plt.subplot(2, 2, 3)
    df['date'] = pd.to_datetime(df['date'])
    daily_sales = df.groupby('date')['sales'].sum()
    plt.plot(daily_sales.index, daily_sales.values, marker='o', linewidth=2, markersize=4)
    plt.title('Pardavimų tendencija')
    plt.xlabel('Data')
    plt.ylabel('Pardavimai')
    plt.xticks(rotation=45)
    
    # Subplot 4: Statistikos lentelė
    plt.subplot(2, 2, 4)
    plt.axis('off')
    stats_text = f"""
    📊 STATISTIKA:
    
    Iš viso įrašų: {len(df)}
    Vidutiniai pardavimai: {df['sales'].mean():.0f}
    Standartinis nuokrypis: {df['sales'].std():.0f}
    
    Geriausias regionas: {df.groupby('region')['sales'].sum().idxmax()}
    Populiariausias produktas: {df.groupby('product')['sales'].sum().idxmax()}
    """
    plt.text(0.1, 0.5, stats_text, fontsize=10, verticalalignment='center')
    
    plt.tight_layout()
    plt.savefig('sales_analysis.png', dpi=300, bbox_inches='tight')
    print("✅ Grafikas išsaugotas kaip 'sales_analysis.png'")
    plt.show()

def mathematical_demo():
    """Matematikos demonstracija"""
    print("\n🧮 Matematikos demonstracija:")
    
    # Fibonacci seka
    def fibonacci(n):
        if n <= 1:
            return n
        return fibonacci(n-1) + fibonacci(n-2)
    
    print("Fibonacci seka (pirmi 10 skaičių):")
    fib_sequence = [fibonacci(i) for i in range(10)]
    print(fib_sequence)
    
    # Atsitiktinių skaičių generavimas ir analizė
    print("\n🎲 Atsitiktinių skaičių analizė:")
    random_numbers = np.random.normal(100, 15, 1000)
    print(f"Vidurkis: {np.mean(random_numbers):.2f}")
    print(f"Standartinis nuokrypis: {np.std(random_numbers):.2f}")
    print(f"Mediana: {np.median(random_numbers):.2f}")

def main():
    """Pagrindinė funkcija"""
    print("🚀 Claude Code Execution Test")
    print("=" * 40)
    print(f"Testas pradėtas: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Analizuojame duomenis
    df = analyze_sales_data()
    
    # Kuriame vizualizaciją
    create_visualization(df)
    
    # Matematikos demo
    mathematical_demo()
    
    print("\n✅ Testas baigtas sėkmingai!")

if __name__ == "__main__":
    main()
