{"name": "@xiaohui-wang/mcpadvisor", "version": "1.0.3", "description": "MCP Advisor & Installation - Find the right MCP server for your needs", "main": "build/index.js", "type": "module", "bin": {"mcpadvisor": "build/index.js"}, "keywords": ["mcp", "model-context-protocol", "ai", "discovery", "recommendation"], "license": "MIT", "packageManager": "pnpm@9.15.0", "dependencies": {"@chatmcp/sdk": "^1.0.6", "@modelcontextprotocol/sdk": "^1.0.3", "@tensorflow-models/universal-sentence-encoder": "^1.3.3", "@tensorflow/tfjs": "^4.22.0", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "axios": "^1.9.0", "config": "^3.3.12", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "meilisearch": "^0.50.0", "mysql2": "^3.14.1", "pino-pretty": "^13.0.0", "ts-node": "^10.9.2", "winston": "^3.17.0", "zod": "^3.24.1"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@types/axios": "^0.9.36", "@types/config": "^3.3.5", "@types/jest": "^29.5.14", "@types/node": "^22.15.14", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "husky": "^9.1.7", "jest": "^29.7.0", "only-allow": "^1.2.1", "prettier": "^3.5.3", "ts-jest": "^29.3.0", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vitest": "^3.0.9"}, "scripts": {"build": "tsc && chmod +x build/index.js", "prepublishOnly": "pnpm run build", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:jest": "jest", "prepare": "husky", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"src/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\"", "check": "pnpm run lint && pnpm run format:check", "deps:update": "pnpm update --latest", "deps:check": "pnpm outdated", "deps:clean": "rm -rf node_modules pnpm-lock.yaml && pnpm install", "preinstall": "npx only-allow pnpm"}, "files": ["build", "README.md", "LICENSE", "data"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/istarwyh/mcpadvisor.git"}, "bugs": {"url": "https://github.com/istarwyh/mcpadvisor/issues"}, "homepage": "https://github.com/istarwyh/mcpadvisor#readme"}