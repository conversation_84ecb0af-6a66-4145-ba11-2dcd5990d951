name: Dependency Check

on:
  schedule:
    - cron: '0 0 * * 0' # 每周日运行
  workflow_dispatch: # 允许手动触发

jobs:
  dependency-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          
      - name: Setup pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 9.15.0
          run_install: false
          
      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV
          
      - uses: actions/cache@v4
        name: Setup pnpm cache
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-
            
      - name: Install dependencies
        run: pnpm install
        
      - name: Check outdated dependencies
        run: pnpm deps:check
        
      - name: Run tests
        run: pnpm test
