[{"id": "rednote-mcp", "name": "rednote-mcp", "display_name": "RedNote MCP", "description": "小红书内容访问的MCP服务，支持关键词搜索笔记、通过URL访问笔记内容、认证管理和命令行初始化工具", "repository": {"type": "git", "url": "https://github.com/ifuryst/rednote-mcp"}, "homepage": "https://github.com/ifuryst/rednote-mcp", "author": {"name": "ifuryst"}, "license": "MIT", "categories": ["Social Media", "Content", "Chinese"], "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "小红书", "社交媒体", "内容获取", "热点"]}, {"id": "mcp-hotnews-server", "name": "mcp-hotnews-server", "display_name": "Mcp-Hotnews-Server", "description": "获取和分析各大平台热点新闻和话题的MCP服务，支持小红书、微博、知乎等多个中文社交媒体平台", "repository": {"type": "git", "url": "https://github.com/wopal-cn/mcp-hotnews-server"}, "homepage": "https://github.com/wopal-cn/mcp-hotnews-server", "author": {"name": "wopal-cn"}, "license": "MIT", "categories": ["News", "Social Media", "Chinese"], "tags": ["热点", "新闻", "小红书", "微博", "知乎"]}, {"id": "firecrawl", "name": "firecrawl", "display_name": "Firecrawl", "description": "Advanced web scraping with JavaScript rendering, PDF support, and smart rate limiting", "repository": {"type": "git", "url": "https://github.com/vrknetha/mcp-server-firecrawl"}, "homepage": "https://github.com/vrknetha/mcp-server-firecrawl", "author": {"name": "vrknetha"}, "license": "MIT", "categories": ["Web Scraping", "AI"], "tags": ["firecrawl", "scraping", "web", "api", "automation"], "examples": [{"title": "Basic Scraping Example", "description": "Scrape content from a single URL", "prompt": "firecrawl_scrape with url 'https://example.com'"}, {"title": "<PERSON><PERSON> Scraping", "description": "Scrape multiple URLs", "prompt": "firecrawl_batch_scrape with urls ['https://example1.com', 'https://example2.com']"}], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "${FIRECRAWL_API_KEY}"}}}, "arguments": {"FIRECRAWL_API_KEY": {"description": "Your FireCrawl API key. Required for using the cloud API (default) and optional for self-hosted instances.", "required": true, "example": "fc-YOUR_API_KEY"}}}, {"id": "rabbitmq", "name": "rabbitmq", "display_name": "RabbitMQ", "description": "The MCP server that interacts with RabbitMQ to publish and consume messages.", "repository": {"type": "git", "url": "https://github.com/kenliao94/mcp-server-rabbitmq"}, "homepage": "https://github.com/kenliao94/mcp-server-rabbitmq", "author": {"name": "kenliao94"}, "license": "MIT", "categories": ["RabbitMQ", "Messaging"], "tags": ["rabbitmq", "server", "messaging"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "https://github.com/kenliao94/mcp-server-rabbitmq", "mcp-server-rabbitmq", "--rabbitmq-host", "${RABBITMQ_HOST}", "--port", "${RABBITMQ_PORT}", "--username", "${RABBITMQ_USERNAME}", "--password", "${RABBITMQ_PASSWORD}", "--use-tls", "${USE_TLS}"]}}, "examples": [{"title": "Publish Message", "description": "Ask <PERSON> to publish a message to a queue.", "prompt": "Please publish a message to the queue."}], "arguments": {"RABBITMQ_HOST": {"description": "The hostname of the RabbitMQ server (e.g., test.rabbit.com, localhost).", "required": true, "example": "test.rabbit.com"}, "RABBITMQ_PORT": {"description": "The port number to connect to the RabbitMQ server (e.g., 5672).", "required": true, "example": "5672"}, "RABBITMQ_USERNAME": {"description": "The username to authenticate with the RabbitMQ server.", "required": true, "example": "guest"}, "RABBITMQ_PASSWORD": {"description": "The password for the RabbitMQ username provided.", "required": true, "example": "guest"}, "USE_TLS": {"description": "Set to true if using TLS (AMQPS), otherwise false.", "required": false, "example": "true or false"}}}, {"id": "aws-cost-explorer", "name": "aws-cost-explorer", "display_name": "AWS Cost Explorer", "description": "Optimize your AWS spend (including Amazon Bedrock spend) with this MCP server by examining spend across regions, services, instance types and foundation models ([demo video](https://www.youtube.com/watch?v=WuVOmYLRFmI&feature=youtu.be)).", "repository": {"type": "git", "url": "https://github.com/aarora79/aws-cost-explorer-mcp-server"}, "homepage": "https://github.com/aarora79/aws-cost-explorer-mcp-server", "author": {"name": "aarora79"}, "license": "MIT", "categories": ["AWS", "Cost Management"], "tags": ["Cost Explorer", "Amazon Bedrock", "AWS"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--directory", "/path/to/aws-cost-explorer-mcp-server", "run", "server.py"], "env": {"AWS_ACCESS_KEY_ID": "${AWS_ACCESS_KEY_ID}", "AWS_SECRET_ACCESS_KEY": "${AWS_SECRET_ACCESS_KEY}", "AWS_REGION": "${AWS_REGION}", "BEDROCK_LOG_GROUP_NAME": "${BEDROCK_LOG_GROUP_NAME}", "MCP_TRANSPORT": "stdio"}}, "docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "--rm", "-e", "AWS_ACCESS_KEY_ID", "-e", "AWS_SECRET_ACCESS_KEY", "-e", "AWS_REGION", "-e", "BEDROCK_LOG_GROUP_NAME", "-e", "MCP_TRANSPORT", "aws-cost-explorer-mcp:latest"], "env": {"AWS_ACCESS_KEY_ID": "${AWS_ACCESS_KEY_ID}", "AWS_SECRET_ACCESS_KEY": "${AWS_SECRET_ACCESS_KEY}", "AWS_REGION": "${AWS_REGION}", "BEDROCK_LOG_GROUP_NAME": "${BEDROCK_LOG_GROUP_NAME}", "MCP_TRANSPORT": "stdio"}}}, "examples": [{"title": "Get EC2 Spending", "description": "Retrieve the EC2 spending data for the previous day.", "prompt": "What was my EC2 spend yesterday?"}, {"title": "Analyze Spending", "description": "Analyze spending by region for the past 14 days.", "prompt": "Analyze my spending by region for the past 14 days."}, {"title": "Show Top Services", "description": "Show me my top 5 AWS services by cost for the last month.", "prompt": "Show me my top 5 AWS services by cost for the last month."}], "arguments": {"AWS_ACCESS_KEY_ID": {"description": "Your AWS Access Key ID required for authenticating API calls to AWS services.", "required": true, "example": "AKIAIOSFODNN7EXAMPLE"}, "AWS_SECRET_ACCESS_KEY": {"description": "Your AWS Secret Access Key required alongside the Access Key ID for authentication.", "required": true, "example": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"}, "AWS_REGION": {"description": "The AWS region where your resources are located. Examples include 'us-east-1', 'eu-west-1'.", "required": true, "example": "us-east-1"}, "BEDROCK_LOG_GROUP_NAME": {"description": "The name of the CloudWatch log group where Amazon Bedrock model invocation logs are stored.", "required": true, "example": "my-bedrock-log-group-name"}}}, {"id": "cfbd-api", "name": "cfbd-api", "display_name": "College Football Data API", "description": "An MCP server for the [College Football Data API](https://collegefootballdata.com/).", "repository": {"type": "git", "url": "https://github.com/lenwood/cfbd-mcp-server"}, "homepage": "https://github.com/lenwood/cfbd-mcp-server", "author": {"name": "lenwood"}, "license": "MIT", "categories": ["Sports", "Data"], "tags": ["football", "college", "API", "statistics"], "installations": {"npm": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/lenwood/cfbd-mcp-server", "src/cfbd_mcp_server/server.py"], "env": {"CFB_API_KEY": "${CFB_API_KEY}", "PATH": "${PATH}"}}}, "examples": [{"title": "Get the largest upset among FCS games during the 2014 season", "description": "Query the server for significant game upsets in the 2014 college football season.", "prompt": "What was the largest upset among FCS games during the 2014 season?"}], "arguments": {"CFB_API_KEY": {"description": "The API key required to authenticate requests to the College Football Data API.", "required": true, "example": "your_api_key_here"}, "PATH": {"description": "Environment variable that specifies the path to the Python executable being used by the server.", "required": false, "example": "/full/path/to/python"}}}, {"id": "redis", "name": "redis", "display_name": "Redis", "description": "MCP server to interact with Redis Server, AWS Memory DB, etc for caching or other use-cases where in-memory and key-value based storage is appropriate", "repository": {"type": "git", "url": "https://github.com/prajwalnayak7/mcp-server-redis"}, "homepage": "https://github.com/prajwalnayak7/mcp-server-redis", "author": {"name": "prajwalnayak7"}, "license": "MIT", "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "https://github.com/prajwalnayak7/mcp-server-redis", "src/server.py"]}}, "examples": [{"title": "Check Redis Connection Status", "description": "User requests the current Redis connection status.", "prompt": "What's the current Redis connection status?"}, {"title": "Store Name in Redis", "description": "User wants to store their name in Redis.", "prompt": "Can you store my name \"<PERSON>\" in Redis?"}, {"title": "Verify Stored Name in Redis", "description": "User wants to verify the value stored in Redis.", "prompt": "Yes please verify it"}]}, {"id": "iterm-mcp", "name": "iterm-mcp", "display_name": "iTerm", "description": "Integration with iTerm2 terminal emulator for macOS, enabling LLMs to execute and monitor terminal commands.", "repository": {"type": "git", "url": "https://github.com/ferrislucas/iterm-mcp"}, "homepage": "https://github.com/ferrislucas/iterm-mcp", "author": {"name": "fer<PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["Terminal"], "tags": ["iTerm", "server", "automation"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "iterm-mcp"]}}}, {"id": "everything-search", "name": "everything-search", "display_name": "Everything Search", "description": "Fast file searching capabilities across Windows (using [Everything SDK](https://www.voidtools.com/support/everything/sdk/)), macOS (using mdfind command), and Linux (using locate/plocate command).", "repository": {"type": "git", "url": "https://github.com/mamertofabian/mcp-everything-search"}, "homepage": "https://github.com/mamertofabian/mcp-everything-search", "author": {"name": "mamertofabian"}, "license": "MIT", "categories": ["Utilities", "Search"], "tags": ["search", "everything"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["mcp-server-everything-search"], "env": {"EVERYTHING_SDK_PATH": "${EVERYTHING_SDK_PATH}"}}, "python": {"type": "python", "command": "python", "args": ["-m", "mcp_server_everything_search"], "env": {"EVERYTHING_SDK_PATH": "${EVERYTHING_SDK_PATH}"}}}, "examples": [{"title": "Search Python files", "description": "Search for all Python files in the system.", "prompt": "{\"query\": \"*.py\",\"max_results\": 50,\"sort_by\": 6}"}, {"title": "Search files modified today", "description": "Find files with the .py extension that were modified today.", "prompt": "{\"query\": \"ext:py datemodified:today\",\"max_results\": 10}"}], "arguments": {"EVERYTHING_SDK_PATH": {"description": "Environment variable that specifies the path to the Everything SDK DLL required for the server to function properly.", "required": true, "example": "path/to/Everything-SDK/dll/Everything64.dll"}}}, {"id": "chroma", "name": "chroma", "display_name": "Chroma", "description": "Vector database server for semantic document search and metadata filtering, built on Chroma", "repository": {"type": "git", "url": "https://github.com/privetin/chroma"}, "homepage": "https://github.com/privetin/chroma", "author": {"name": "privetin"}, "license": "MIT", "categories": ["database", "server"], "tags": ["vector database", "semantic search"], "examples": [{"title": "Create a document", "description": "Creates a new document with specified content and metadata.", "prompt": "create_document({\"document_id\": \"ml_paper1\", \"content\": \"Convolutional neural networks improve image recognition accuracy.\", \"metadata\": {\"year\": 2020, \"field\": \"computer vision\", \"complexity\": \"advanced\"}})"}, {"title": "Search similar documents", "description": "Finds documents semantically similar to a given query.", "prompt": "search_similar({\"query\": \"machine learning models\", \"num_results\": 2, \"metadata_filter\": {\"year\": 2020, \"field\": \"computer vision\"}})"}], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/privetin/chroma", "src/chroma/server.py"]}}}, {"id": "kubernetes", "name": "kubernetes", "display_name": "Kubernetes", "description": "Connect to Kubernetes cluster and manage pods, deployments, and services.", "repository": {"type": "git", "url": "https://github.com/Flux159/mcp-server-kubernetes"}, "homepage": "https://github.com/Flux159/mcp-server-kubernetes", "author": {"name": "Flux159"}, "license": "[NOT GIVEN]", "categories": ["Kubernetes", "Server"], "tags": ["kubernetes", "server", "management"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "mcp-server-kubernetes"]}}}, {"id": "contentful-mcp", "name": "contentful-mcp", "display_name": "Contentful Management", "description": "Read, update, delete, publish content in your [Contentful](https://contentful.com/) space(s) from this MCP Server.", "repository": {"type": "git", "url": "https://github.com/ivo-toby/contentful-mcp"}, "homepage": "https://github.com/ivo-toby/contentful-mcp", "author": {"name": "ivo-toby"}, "license": "MIT", "categories": ["Content Management"], "tags": ["Contentful", "Management API", "CRUD Operations"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@ivotoby/contentful-management-mcp-server"], "env": {"CONTENTFUL_MANAGEMENT_ACCESS_TOKEN": "${CONTENTFUL_MANAGEMENT_ACCESS_TOKEN}"}}}, "arguments": {"CONTENTFUL_MANAGEMENT_ACCESS_TOKEN": {"description": "Your Content Management API token for accessing Contentful services.", "required": true, "example": "<Your CMA token>"}}}, {"id": "deepseek-mcp-server", "name": "deepseek-mcp-server", "display_name": "DeepSeek", "description": "Model Context Protocol server integrating DeepSeek's advanced language models, in addition to [other useful API endpoints](https://github.com/DMontgomery40/deepseek-mcp-server?tab=readme-ov-file#features)", "repository": {"type": "git", "url": "https://github.com/DMontgomery40/deepseek-mcp-server"}, "homepage": "https://github.com/DMontgomery40/deepseek-mcp-server", "author": {"name": "DMontgomery40"}, "license": "MIT", "categories": ["API", "Server"], "tags": ["DeepSeek", "API", "Language Model"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "deepseek-mcp-server"], "env": {"DEEPSEEK_API_KEY": "${DEEPSEEK_API_KEY}"}}}, "arguments": {"DEEPSEEK_API_KEY": {"description": "An API key required to authenticate requests to the DeepSeek API.", "required": true, "example": "your-api-key"}}}, {"id": "gitlab", "name": "gitlab", "display_name": "GitLab", "description": "GitLab API, enabling project management", "repository": {"type": "git", "url": "https://github.com/modelcontextprotocol/servers"}, "homepage": "https://github.com/modelcontextprotocol/servers/blob/main/src/gitlab", "author": {"name": "modelcontextprotocol"}, "license": "MIT", "categories": ["Development", "Collaboration"], "tags": ["GitLab", "API"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-gitlab"], "env": {"GITLAB_PERSONAL_ACCESS_TOKEN": "${GITLAB_PERSONAL_ACCESS_TOKEN}", "GITLAB_API_URL": "${GITLAB_API_URL}"}}, "docker": {"type": "docker", "command": "docker", "args": ["run", "--rm", "-i", "-e", "GITLAB_PERSONAL_ACCESS_TOKEN", "-e", "GITLAB_API_URL", "mcp/gitlab"], "env": {"GITLAB_PERSONAL_ACCESS_TOKEN": "${GITLAB_PERSONAL_ACCESS_TOKEN}", "GITLAB_API_URL": "${GITLAB_API_URL}"}}}, "arguments": {"GITLAB_PERSONAL_ACCESS_TOKEN": {"description": "Your GitLab personal access token", "required": true}, "GITLAB_API_URL": {"description": "Base URL for GitLab API", "required": false, "example": "https://gitlab.com/api/v4"}}}, {"id": "dune-analytics-mcp", "name": "dune-analytics-mcp", "display_name": "<PERSON>ne Analytics", "description": "A mcp server that bridges Dune Analytics data to AI agents.", "repository": {"type": "git", "url": "https://github.com/kukapay/dune-analytics-mcp"}, "homepage": "https://github.com/kukapay/dune-analytics-mcp", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["Analytics", "Data Processing"], "tags": ["Dune", "Analytics", "AI agents"], "examples": [{"title": "Get Latest Result", "description": "Retrieves the latest results of a specified Dune query.", "prompt": "get_latest_result(query_id=4853921)"}, {"title": "Run Query", "description": "Executes a Dune query and returns the results.", "prompt": "run_query(query_id=1215383)"}], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/kukapay/dune-analytics-mcp", "main.py"], "env": {"DUNE_API_KEY": "${DUNE_API_KEY}"}}}, "arguments": {"DUNE_API_KEY": {"description": "A valid Dune Analytics API key obtained from Dune Analytics for authentication and data access.", "required": true, "example": "your_api_key_here"}}}, {"id": "whois-mcp", "name": "whois-mcp", "display_name": "<PERSON><PERSON>", "description": "MCP server that performs whois lookup against domain, IP, ASN and TLD.", "repository": {"type": "git", "url": "https://github.com/bharath<PERSON><PERSON>-gane<PERSON>/whois-mcp"}, "homepage": "https://github.com/bharath<PERSON><PERSON>-gane<PERSON>/whois-mcp", "author": {"name": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["Networking", "Lookup"], "tags": ["whois", "domain", "tools"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@bharathvaj/whois-mcp@latest"]}}, "examples": [{"title": "Look up WHOIS information", "description": "Using the Whois MCP to find out domain details.", "prompt": "What can you tell me about example.com?"}]}, {"id": "deepseek-thinker-mcp", "name": "deepseek-thinker-mcp", "display_name": "Deepseek Thinker", "description": "A MCP (Model Context Protocol) provider Deepseek reasoning content to MCP-enabled AI Clients, like <PERSON>. Supports access to Deepseek's thought processes from the Deepseek API service or from a local Ollama server.", "repository": {"type": "git", "url": "https://github.com/ruixingshi/deepseek-thinker-mcp"}, "license": "MIT", "categories": ["AI", "Reasoning"], "tags": ["Deepseek", "AI Clients", "Reasoning"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "deepseek-thinker-mcp"], "env": {"API_KEY": "${API_KEY}", "BASE_URL": "${BASE_URL}"}}}, "author": {"name": "ruixing<PERSON>"}, "homepage": "https://github.com/ruixingshi/deepseek-thinker-mcp", "arguments": {"API_KEY": {"description": "Your OpenAI API Key for authentication with the OpenAI services.", "required": true, "example": "sk-xxxxxxxxxx"}, "BASE_URL": {"description": "The base URL for the OpenAI API that you are connecting to.", "required": true, "example": "https://api.openai.com/v1"}}}, {"id": "git", "name": "git", "display_name": "git", "description": "Tools to read, search, and manipulate Git repositories", "repository": {"type": "git", "url": "https://github.com/modelcontextprotocol/servers"}, "homepage": "https://github.com/modelcontextprotocol/servers/blob/main/src/git", "author": {"name": "modelcontextprotocol"}, "license": "MIT", "categories": ["Git"], "tags": ["Git", "Server", "Automation"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["mcp-server-git", "--repository", "${GIT_REPO_PATH}"]}, "docker": {"type": "docker", "command": "docker", "args": ["run", "--rm", "-i", "--mount", "type=bind,src=${GIT_REPO_PATH},dst=${GIT_REPO_PATH}", "mcp/git"]}, "python": {"type": "python", "command": "python", "args": ["-m", "mcp_server_git", "--repository", "${GIT_REPO_PATH}"]}}, "arguments": {"GIT_REPO_PATH": {"description": "The path to the Git repository that the mcp-server-git will interact with.", "required": true, "example": "/path/to/git/repo"}}}, {"id": "code-executor", "name": "code-executor", "display_name": "Code Executor", "description": "An MCP server that allows LLMs to execute Python code within a specified Conda environment.", "repository": {"type": "git", "url": "https://github.com/bazinga012/mcp_code_executor"}, "homepage": "https://github.com/bazinga012/mcp_code_executor", "author": {"name": "bazinga012"}, "license": "MIT", "categories": ["Server", "Code Execution"], "tags": ["Python", "Conda", "Execution"], "examples": [{"title": "Execute Python Code", "description": "An example of executing Python code using MCP Code Executor", "prompt": "Please execute the following code: print('Hello, World!')"}], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/bazinga012/mcp_code_executor"], "env": {"CODE_STORAGE_DIR": "${CODE_STORAGE_DIR}", "CONDA_ENV_NAME": "${CONDA_ENV_NAME}"}}}, "arguments": {"CODE_STORAGE_DIR": {"description": "The directory where the generated code will be stored.", "required": true, "example": "/path/to/code/storage"}, "CONDA_ENV_NAME": {"description": "The name of the Conda environment in which the code will be executed.", "required": true, "example": "your-conda-env"}}}, {"id": "world-bank-data-api", "name": "world-bank-data-api", "display_name": "World Bank Data API", "description": "A server that fetches data indicators available with the World Bank as part of their data API", "repository": {"type": "git", "url": "https://github.com/anshumax/world_bank_mcp_server"}, "homepage": "https://github.com/anshumax/world_bank_mcp_server", "author": {"name": "an<PERSON><PERSON>"}, "license": "[NOT GIVEN]", "categories": ["Data API"], "tags": ["World Bank", "Data", "API", "Indicators", "Analysis"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/anshumax/world_bank_mcp_server", "src/world_bank_mcp_server/server.py"]}}, "examples": [{"title": "List Countries", "description": "Lists available countries in the World Bank open data API.", "prompt": "List all countries available in the World Bank data."}, {"title": "List Indicators", "description": "Lists available indicators in the World Bank open data API.", "prompt": "List all indicators available in the World Bank data."}, {"title": "Analyze Indicators", "description": "Analyzes specific indicators for a selected country.", "prompt": "Analyze the poverty indicators for Kenya."}]}, {"id": "firebase", "name": "firebase", "display_name": "Firebase", "description": "Server to interact with Firebase services including Firebase Authentication, Firestore, and Firebase Storage.", "repository": {"type": "git", "url": "https://github.com/gannonh/firebase-mcp"}, "homepage": "https://github.com/gannonh/firebase-mcp", "author": {"name": "gann<PERSON><PERSON>"}, "license": "MIT", "categories": ["Firebase", "LLM"], "tags": ["Firebase", "LLM", "Server"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@gannonh/firebase-mcp"], "env": {"SERVICE_ACCOUNT_KEY_PATH": "${SERVICE_ACCOUNT_KEY_PATH}", "FIREBASE_STORAGE_BUCKET": "${FIREBASE_STORAGE_BUCKET}"}}}, "arguments": {"SERVICE_ACCOUNT_KEY_PATH": {"description": "Path to your Firebase service account key JSON file", "required": true, "example": "/absolute/path/to/serviceAccountKey.json"}, "FIREBASE_STORAGE_BUCKET": {"description": "Bucket name for Firebase Storage", "required": false, "example": "your-project-id.firebasestorage.app"}}}, {"id": "dify", "name": "dify", "display_name": "Dify", "description": "A simple implementation of an MCP server for dify workflows.", "repository": {"type": "git", "url": "https://github.com/YanxingLiu/dify-mcp-server"}, "homepage": "https://github.com/YanxingLiu/dify-mcp-server", "author": {"name": "Yan<PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["Server", "Workflows"], "tags": ["dify", "server", "workflows"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/YanxingLiu/dify-mcp-server", "${DIFY_MCP_SERVER}"], "env": {"CONFIG_PATH": "${CONFIG_PATH}"}}}, "arguments": {"DIFY_MCP_SERVER": {"description": "This argument specifies the path to the Dify MCP server that needs to be executed.", "required": true, "example": "/path/to/dify_mcp_server"}, "CONFIG_PATH": {"description": "This environment variable indicates the path to the configuration file for the Dify MCP server, typically a YAML file containing necessary settings.", "required": true, "example": "/Users/<USER>/Downloads/config.yaml"}}}, {"id": "code-sandbox-mcp", "name": "code-sandbox-mcp", "display_name": "Code Sandbox", "description": "An MCP server to create secure code sandbox environment for executing code within Docker containers.", "repository": {"type": "git", "url": "https://github.com/Automata-Labs-team/code-sandbox-mcp"}, "homepage": "https://github.com/Automata-Labs-team/code-sandbox-mcp", "author": {"name": "Automata-Labs-team"}, "license": "MIT", "installations": {"custom": {"type": "custom", "command": "/path/to/code-sandbox-mcp", "args": [], "env": {}}}, "categories": ["Development Tools", "AI Tools"], "tags": ["<PERSON>er", "Sandbox", "Code Execution"]}, {"id": "rijksmuseum", "name": "rijksmuseum", "display_name": "Rijksmuseum", "description": "Interface with the Rijksmuseum API to search artworks, retrieve artwork details, access image tiles, and explore user collections.", "repository": {"type": "git", "url": "https://github.com/r-huijts/rijksmuseum-mcp"}, "homepage": "https://github.com/r-huijts/rijksmuseum-mcp", "author": {"name": "r-huijts"}, "license": "MIT", "categories": ["art", "museum"], "tags": ["collection", "Rijksmuseum"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "mcp-server-rijksmuseum"], "env": {"RIJKSMUSEUM_API_KEY": "${RIJKSMUSEUM_API_KEY}"}}}, "examples": [{"title": "Artwork Discovery", "description": "Queries related to discovering artworks in the museum's collection.", "prompt": "\"Show me all paintings by <PERSON><PERSON><PERSON><PERSON> from the 1640s\""}, {"title": "Artwork Analysis", "description": "Queries related to analyzing specific artworks.", "prompt": "\"Tell me everything about The Night Watch\""}, {"title": "Artist Research", "description": "Queries focused on researching artists and their works.", "prompt": "\"Create a timeline of <PERSON><PERSON><PERSON><PERSON>'s self-portraits\""}, {"title": "Thematic Exploration", "description": "Queries that explore themes in the artworks.", "prompt": "\"Find all artworks depicting biblical scenes\""}, {"title": "Collection Analysis", "description": "Queries about user-curated collections.", "prompt": "\"Show me the most popular user-curated collections\""}, {"title": "Visual Details", "description": "Queries for examining visual details in artworks.", "prompt": "\"Let me examine the details in the background of The Night Watch\""}], "arguments": {"RIJKSMUSEUM_API_KEY": {"description": "Your Rijksmuseum API key used for authenticating requests to the Rijksmuseum API.", "required": true, "example": "your_api_key_here"}}}, {"id": "mem0-mcp", "name": "mem0-mcp", "display_name": "Mem0 Server", "description": "A Model Context Protocol server for Mem0, which helps with managing coding preferences.", "repository": {"type": "git", "url": "https://github.com/mem0ai/mem0-mcp"}, "homepage": "https://github.com/mem0ai/mem0-mcp", "author": {"name": "mem0ai"}, "license": "MIT", "categories": ["development"], "tags": ["coding preferences", "mem0"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/mem0ai/mem0-mcp", "main.py"]}}, "arguments": {"host": {"description": "The host address that the server will bind to. This can be configured to allow access from different IP addresses or set it to 'localhost' for local access only.", "required": false, "example": "0.0.0.0"}, "port": {"description": "The port number on which the server will listen for incoming connections. Changing this can help to avoid port conflicts with other services on the same machine.", "required": false, "example": "8080"}}}, {"id": "slack", "name": "slack", "display_name": "<PERSON><PERSON>ck", "description": "Channel management and messaging capabilities", "repository": {"type": "git", "url": "https://github.com/modelcontextprotocol/servers"}, "license": "MIT", "categories": ["communication", "integration"], "tags": ["slack", "api", "bot"], "examples": [{"title": "Post a message to a channel", "description": "Send a message to a specified Slack channel.", "prompt": "Include the channel ID and the message text."}], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-slack"], "env": {"SLACK_BOT_TOKEN": "${SLACK_BOT_TOKEN}", "SLACK_TEAM_ID": "${SLACK_TEAM_ID}"}}, "docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "--rm", "-e", "SLACK_BOT_TOKEN", "-e", "SLACK_TEAM_ID", "mcp/slack"], "env": {"SLACK_BOT_TOKEN": "${SLACK_BOT_TOKEN}", "SLACK_TEAM_ID": "${SLACK_TEAM_ID}"}}}, "author": {"name": "modelcontextprotocol"}, "homepage": "https://github.com/modelcontextprotocol/servers/blob/main/src/slack", "arguments": {"SLACK_BOT_TOKEN": {"description": "The OAuth token for the bot user in the Slack workspace, used for authenticating API requests.", "required": true, "example": "xoxb-your-bot-token"}, "SLACK_TEAM_ID": {"description": "The unique identifier of the Slack workspace, required for operations within the workspace.", "required": true, "example": "*********"}}}, {"id": "openai-websearch-mcp", "name": "openai-websearch-mcp", "display_name": "OpenAI WebSearch", "description": "This is a Python-based MCP server that provides OpenAI `web_search` build-in tool.", "repository": {"type": "git", "url": "https://github.com/ConechoAI/openai-websearch-mcp"}, "homepage": "https://github.com/ConechoAI/openai-websearch-mcp", "author": {"name": "ConechoAI"}, "license": "MIT", "categories": ["AI", "Web Search"], "tags": ["openai", "websearch", "AI assistant"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["openai-websearch-mcp"], "env": {"OPENAI_API_KEY": "${OPENAI_API_KEY}"}}, "python": {"type": "python", "command": "python", "args": ["-m", "openai_websearch_mcp"], "env": {"OPENAI_API_KEY": "${OPENAI_API_KEY}"}}}, "examples": [{"title": "Using web search", "description": "Perform a web search using the OpenAI WebSearch MCP server.", "prompt": "search('latest news on AI')"}], "arguments": {"OPENAI_API_KEY": {"description": "Your OpenAI API key to authenticate requests to the OpenAI API.", "required": true, "example": "sk-xxxx"}}}, {"id": "linear", "name": "linear", "display_name": "Linear", "description": "Allows LLM to interact with Linear's API for project management, including searching, creating, and updating issues.", "repository": {"type": "git", "url": "https://github.com/jerhadf/linear-mcp-server"}, "homepage": "https://github.com/jerhadf/linear-mcp-server", "author": {"name": "j<PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["integration", "api"], "tags": ["linear", "issue tracking", "LLM"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "linear-mcp-server"], "env": {"LINEAR_API_KEY": "${LINEAR_API_KEY}"}}}, "examples": [{"title": "Show me all my high-priority issues", "description": "Execute the search_issues tool and/or linear-user:///{userId}/assigned to find issues assigned to the user with priority 1", "prompt": "Show me all my high-priority issues"}, {"title": "Create a bug report", "description": "Use create_issue to create a new high-priority issue with appropriate details and status tracking.", "prompt": "Based on what I've told you about this bug already, make a bug report for the authentication system"}, {"title": "Find all in-progress frontend tasks", "description": "Use search_issues to locate frontend-related issues with in progress status.", "prompt": "Find all in progress frontend tasks"}, {"title": "Get summary of recent updates", "description": "Use search_issues to identify relevant issue(s) and fetch the issue details.", "prompt": "Give me a summary of recent updates on the issues for mobile app development"}, {"title": "Analyze current workload for the mobile team", "description": "Combine linear-team:///{teamId}/issues and search_issues to analyze issue distribution and priorities across the mobile team.", "prompt": "What's the current workload for the mobile team?"}], "arguments": {"LINEAR_API_KEY": {"description": "Your Linear API key to authenticate requests to the Linear API.", "required": true, "example": "your_api_key_here"}}}, {"id": "mcp-create", "name": "mcp-create", "display_name": "Create Server", "description": "A dynamic MCP server management service that creates, runs, and manages Model Context Protocol servers on-the-fly.", "repository": {"type": "git", "url": "https://github.com/tesla0225/mcp-create"}, "homepage": "https://github.com/tesla0225/mcp-create", "author": {"name": "tesla0225"}, "license": "MIT", "categories": ["Server Management"], "tags": ["dynamic", "TypeScript"], "installations": {"docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "--rm", "mcp-create"]}}, "examples": [{"title": "Creating a New Server", "description": "Example of creating a new server using TypeScript.", "prompt": "{\"name\":\"create-server-from-template\",\"arguments\":{\"language\":\"typescript\"}}"}, {"title": "Executing a Tool", "description": "Example of executing a tool on a server.", "prompt": "{\"name\":\"execute-tool\",\"arguments\":{\"serverId\":\"ba7c9a4f-6ba8-4cad-8ec8-a41a08c19fac\",\"toolName\":\"echo\",\"args\":{\"message\":\"Hello, dynamic MCP server!\"}}}"}]}, {"id": "okta", "name": "okta", "display_name": "Okta", "description": "Interact with Okta API.", "repository": {"type": "git", "url": "https://github.com/kapilduraphe/okta-mcp-server"}, "homepage": "https://github.com/kapilduraphe/okta-mcp-server", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["user-management", "api"], "tags": ["Okta", "user management", "group management"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/kapilduraphe/okta-mcp-server"], "env": {"OKTA_ORG_URL": "${OKTA_ORG_URL}", "OKTA_API_TOKEN": "${OKTA_API_TOKEN}"}}}, "examples": [{"title": "Show user details", "description": "Retrieve details for a specific user.", "prompt": "Show me details for user with userId XXXX"}, {"title": "Check user status", "description": "Get the status of a specific user.", "prompt": "What's the status <NAME_EMAIL>"}, {"title": "Last login info", "description": "Find out when a user last logged in.", "prompt": "When was the last login <NAME_EMAIL>"}, {"title": "List users by department", "description": "Get a list of all users in the marketing department.", "prompt": "List all users in the marketing department"}, {"title": "Find recent users", "description": "Retrieve users created in the last month.", "prompt": "Find users created in the last month"}, {"title": "Show user groups", "description": "List all groups in the Okta organization.", "prompt": "Show me all the groups in my Okta organization"}, {"title": "Admin groups", "description": "List groups that contain the word 'admin'.", "prompt": "List groups containing the word 'admin'"}], "arguments": {"OKTA_ORG_URL": {"description": "The base URL for your Okta organization, should include 'https://'.", "required": true, "example": "https://dev-123456.okta.com"}, "OKTA_API_TOKEN": {"description": "A valid API token used to authenticate API requests to Okta.", "required": true}}}, {"id": "base-free-usdc-transfer", "name": "base-free-usdc-transfer", "display_name": "Free USDC Transfer", "description": "Send USDC on [Base](https://base.org/) for free using Claude AI! Built with [Coinbase CDP](https://docs.cdp.coinbase.com/mpc-wallet/docs/welcome).", "repository": {"type": "git", "url": "https://github.com/magnetai/mcp-free-usdc-transfer"}, "homepage": "https://github.com/magnetai/mcp-free-usdc-transfer", "author": {"name": "magnetai"}, "license": "MIT", "categories": ["Finance", "Blockchain"], "tags": ["USDC", "Base", "Coinbase", "MPC Wallet"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@magnetai/free-usdc-transfer"], "env": {"COINBASE_CDP_API_KEY_NAME": "${COINBASE_CDP_API_KEY_NAME}", "COINBASE_CDP_PRIVATE_KEY": "${COINBASE_CDP_PRIVATE_KEY}"}}}, "arguments": {"COINBASE_CDP_API_KEY_NAME": {"description": "The name of your Coinbase CDP API key, which is required for authenticating API requests.", "required": true, "example": "my_api_key_name"}}}, {"id": "ma<PERSON>b", "name": "ma<PERSON>b", "display_name": "MariaDB Database Integration", "description": "MariaDB database integration with configurable access controls in Python.", "repository": {"type": "git", "url": "https://github.com/abel9851/mcp-server-mariadb"}, "homepage": "https://github.com/abel9851/mcp-server-mariadb", "author": {"name": "abel9851"}, "license": "MIT", "categories": ["Database"], "tags": ["MariaDB", "Data Retrieval"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["mcp-server-mariadb", "--host", "${DB_HOST}", "--port", "${DB_PORT}", "--user", "${DB_USER}", "--password", "${DB_PASSWORD}", "--database", "${DB_NAME}"]}}, "examples": [{"title": "Query Database", "description": "Example of executing a read-only operation against MariaDB.", "prompt": "Execute read-only operations against your MariaDB database."}], "arguments": {"DB_HOST": {"description": "The hostname of the MariaDB server to connect to.", "required": true, "example": "localhost"}, "DB_PORT": {"description": "The port number on which the MariaDB server is listening.", "required": true, "example": "3306"}, "DB_USER": {"description": "The username to connect to the MariaDB database.", "required": true, "example": "root"}, "DB_PASSWORD": {"description": "The password for the MariaDB user.", "required": true}, "DB_NAME": {"description": "The name of the database to connect to.", "required": true}}}, {"id": "servicenow", "name": "servicenow", "display_name": "ServiceNow", "description": "A MCP server to interact with a ServiceNow instance", "repository": {"type": "git", "url": "https://github.com/osomai/servicenow-mcp"}, "homepage": "https://github.com/osomai/servicenow-mcp", "author": {"name": "osoma<PERSON>"}, "license": "MIT", "categories": ["Integration", "Automation"], "tags": ["ServiceNow", "Automation"], "examples": [{"title": "Incident Management - Creating an Incident", "description": "Create a new incident for a network outage in the east region.", "prompt": "Create a new incident for a network outage in the east region."}, {"title": "Service Catalog - List Items", "description": "Show me all items in the service catalog.", "prompt": "Show me all items in the service catalog."}], "installations": {"python": {"type": "python", "command": "/Users/<USER>/dev/servicenow-mcp/.venv/bin/python", "args": ["-m", "servicenow_mcp.cli"], "env": {"SERVICENOW_INSTANCE_URL": "${SERVICENOW_INSTANCE_URL}", "SERVICENOW_USERNAME": "${SERVICENOW_USERNAME}", "SERVICENOW_PASSWORD": "${SERVICENOW_PASSWORD}", "SERVICENOW_AUTH_TYPE": "${SERVICENOW_AUTH_TYPE}"}}}, "arguments": {"SERVICENOW_INSTANCE_URL": {"description": "URL of the ServiceNow instance to connect to.", "required": true, "example": "https://your-instance.service-now.com"}, "SERVICENOW_USERNAME": {"description": "Username for accessing the ServiceNow instance.", "required": true, "example": "your-username"}, "SERVICENOW_PASSWORD": {"description": "Password for the ServiceNow username.", "required": true, "example": "your-password"}, "SERVICENOW_AUTH_TYPE": {"description": "Authentication type for connecting to ServiceNow. Options are 'basic', 'oauth', or 'api_key'.", "required": true, "example": "basic"}}}, {"id": "mcp-compass", "name": "mcp-compass", "display_name": "<PERSON>mp<PERSON>", "description": "Suggest the right MCP server for your needs", "repository": {"type": "git", "url": "https://github.com/liuyoshio/mcp-compass"}, "homepage": "https://github.com/liuyoshio/mcp-compass", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["discovery", "recommendation", "AI"], "tags": ["compass", "service discovery"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@liuyoshio/mcp-compass"]}}}, {"id": "alphavantage", "name": "alphavantage", "display_name": "Alphavantage", "description": "MCP server for stock market data API [AlphaVantage](https://www.alphavantage.co/)", "repository": {"type": "git", "url": "https://github.com/calvernaz/alphavantage"}, "homepage": "https://github.com/calvernaz/alphavantage", "author": {"name": "calvernaz"}, "license": "Apache-2.0", "categories": ["API", "Stock Market"], "tags": ["alphavantage", "stock market"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/calvernaz/alphavantage.git", "alphavantage"], "env": {"ALPHAVANTAGE_API_KEY": "${ALPHAVANTAGE_API_KEY}"}}}, "arguments": {"ALPHAVANTAGE_API_KEY": {"description": "The API key to access the Alphavantage service.", "required": true, "example": "YOUR_API_KEY_HERE"}}}, {"id": "drupal", "name": "drupal", "display_name": "Drupal Server", "description": "Server for interacting with [<PERSON><PERSON><PERSON>](https://www.drupal.org/project/mcp) using STDIO transport layer.", "repository": {"type": "git", "url": "https://github.com/Omedia/mcp-server-drupal"}, "homepage": "https://github.com/Omedia/mcp-server-drupal", "author": {"name": "Omedia"}, "license": "MIT", "categories": ["CMS", "Server"], "tags": ["<PERSON><PERSON><PERSON>", "TypeScript"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "deno", "run", "-A", "jsr:@omedia/mcp-server-drupal@${VERSION}", "--drupal-url", "${DRUPAL_BASE_URL}"], "env": {}}}, "arguments": {"VERSION": {"description": "The version of the MCP server to be used. This must be provided to ensure compatibility with the installed Drupal version.", "required": true, "example": "1.0.0"}, "DRUPAL_BASE_URL": {"description": "The base URL of the Drupal site that the MCP server will interact with.", "required": true, "example": "https://example.com"}}}, {"id": "placid-app", "name": "placid-app", "display_name": "Placid.app", "description": "Generate image and video creatives using Placid.app templates", "repository": {"type": "git", "url": "https://github.com/felores/placid-mcp-server"}, "homepage": "https://github.com/felores/placid-mcp-server", "author": {"name": "felores"}, "license": "MIT", "categories": ["API"], "tags": ["Placid", "Templates", "Image Generation", "Video Generation"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["@felores/placid-mcp-server"], "env": {"PLACID_API_TOKEN": "${PLACID_API_TOKEN}"}}}, "examples": [{"title": "Generate Video Example", "description": "Example usage for generating a video using Placid templates.", "prompt": "{\"template_id\":\"template-uuid\",\"layers\":{\"MEDIA\":{\"video\":\"https://example.com/video.mp4\"},\"PHOTO\":{\"image\":\"https://example.com/photo.jpg\"},\"LOGO\":{\"image\":\"https://example.com/logo.png\"},\"HEADLINE\":{\"text\":\"My Video Title\"}},\"audio\":\"https://example.com/background.mp3\",\"audio_duration\":\"auto\"}"}, {"title": "Generate Image Example", "description": "Example usage for generating an image using Placid templates.", "prompt": "{\"template_id\":\"template-uuid\",\"layers\":{\"headline\":{\"text\":\"Welcome to My App\"},\"background\":{\"image\":\"https://example.com/bg.jpg\"}}}"}], "arguments": {"PLACID_API_TOKEN": {"description": "Your Placid API token used for authenticating requests to the Placid API.", "required": true, "example": "my-secret-api-token"}}}, {"id": "siri-shortcuts", "name": "siri-shortcuts", "display_name": "<PERSON><PERSON> Shortcuts", "description": "MCP to interact with Siri Shortcuts on macOS. Exposes all Shortcuts as MCP tools.", "repository": {"type": "git", "url": "https://github.com/dvcrn/mcp-server-siri-shortcuts"}, "homepage": "https://github.com/dvcrn/mcp-server-siri-shortcuts", "author": {"name": "dvcrn"}, "license": "[NOT GIVEN]", "categories": ["shortcuts", "macos"], "tags": ["siri", "shortcuts", "automation"], "examples": [{"title": "List all shortcuts", "description": "Fetches all available Siri shortcuts", "prompt": "list_shortcuts"}, {"title": "Run a specific shortcut", "description": "Execute a shortcut with optional input", "prompt": "run_shortcut_My_Shortcut_1"}], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["mcp-server-siri-shortcuts"]}}}, {"id": "windows-cli", "name": "windows-cli", "display_name": "Windows CLI", "description": "MCP server for secure command-line interactions on Windows systems, enabling controlled access to PowerShell, CMD, and Git Bash shells.", "repository": {"type": "git", "url": "https://github.com/SimonB97/win-cli-mcp-server"}, "homepage": "https://github.com/SimonB97/win-cli-mcp-server", "author": {"name": "SimonB97"}, "license": "MIT", "categories": ["CLI", "Windows"], "tags": ["CLI", "Windows", "Security", "SSH"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@simonb97/server-win-cli", "--config", "${config}"]}}, "examples": [{"title": "Usage with <PERSON>", "description": "Add MCP server configuration to <PERSON>.", "prompt": "\n{\n  \"mcpServers\": {\n    \"windows-cli\": {\n      \"command\": \"npx\",\n      \"args\": [\"-y\", \"@simonb97/server-win-cli\"]\n    }\n  }\n}\n"}], "arguments": {"config": {"description": "The path to your configuration file, which customizes the server behavior.", "required": true, "example": "path/to/your/config.json"}}}, {"id": "x-twitter", "name": "x-twitter", "display_name": "X (Twitter)", "description": "Create, manage and publish X/Twitter posts directly through <PERSON> chat.", "repository": {"type": "git", "url": "https://github.com/vidhupv/x-mcp"}, "homepage": "https://github.com/vidhupv/x-mcp", "author": {"name": "vidhupv"}, "license": "MIT", "categories": ["Social Media"], "tags": ["Twitter", "X"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/vidhupv/x-mcp", "x-mcp"], "env": {"TWITTER_API_KEY": "${TWITTER_API_KEY}", "TWITTER_API_SECRET": "${TWITTER_API_SECRET}", "TWITTER_ACCESS_TOKEN": "${TWITTER_ACCESS_TOKEN}", "TWITTER_ACCESS_TOKEN_SECRET": "${TWITTER_ACCESS_TOKEN_SECRET}"}}}, "examples": [{"title": "Tweet", "description": "Example of sending a tweet through <PERSON> chat.", "prompt": "Tweet 'Just learned how to tweet through AI - mind blown! 🤖✨'"}, {"title": "Create Thread", "description": "Create a thread about a specific topic.", "prompt": "Create a thread about the history of pizza"}, {"title": "Show Drafts", "description": "Request to see draft tweets.", "prompt": "Show me my draft tweets"}, {"title": "Publish Draft", "description": "Publish an existing draft.", "prompt": "Publish this draft!"}, {"title": "Delete Draft", "description": "Delete a specific draft.", "prompt": "Delete that draft"}], "arguments": {"TWITTER_API_KEY": {"description": "The API key for accessing Twitter's API.", "required": true, "example": "your_api_key"}, "TWITTER_API_SECRET": {"description": "The API secret key for accessing Twitter's API.", "required": true, "example": "your_api_secret"}, "TWITTER_ACCESS_TOKEN": {"description": "The access token for authorizing the application to access Twitter on behalf of the user.", "required": true, "example": "your_access_token"}, "TWITTER_ACCESS_TOKEN_SECRET": {"description": "The access token secret for authorizing the application to access Twitter on behalf of the user.", "required": true, "example": "your_access_token_secret"}}}, {"id": "chatmcp", "name": "chatmcp", "display_name": "Chat Desktop App", "description": "– An Open Source Cross-platform GUI Desktop application compatible with Linux, macOS, and Windows, enabling seamless interaction with MCP servers across dynamically selectable LLMs, by **[AIQL](https://github.com/AI-QL/chat-mcp)**", "repository": {"type": "git", "url": "https://github.com/AI-QL/chat-mcp"}, "homepage": "https://github.com/AI-QL/chat-mcp", "author": {"name": "AIQL"}, "license": "Apache-2.0", "categories": ["chat", "desktop", "app", "LLMs"], "tags": ["LLM", "Electron", "cross-platform"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/AI-QL/chat-mcp"], "env": {"PATH": "${PATH}"}}}, "arguments": {"PATH": {"description": "This environment variable specifies the system's executable search path, which determines where the operating system looks for executable files when running commands.", "required": false, "example": "C:\\Program Files\\nodejs;C:\\Windows\\System32"}}}, {"id": "monday-com", "name": "monday-com", "display_name": "Monday.com", "description": "MCP Server to interact with Monday.com boards and items.", "repository": {"type": "git", "url": "https://github.com/sakce/mcp-server-monday"}, "homepage": "https://github.com/sakce/mcp-server-monday", "author": {"name": "sakce"}, "license": "MIT", "categories": ["productivity"], "tags": ["monday.com", "API"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["mcp-server-monday"], "env": {"MONDAY_API_KEY": "${MONDAY_API_KEY}", "MONDAY_WORKSPACE_NAME": "${MONDAY_WORKSPACE_NAME}"}}, "docker": {"type": "docker", "command": "docker", "args": ["run", "--rm", "-i", "-e", "MONDAY_API_KEY=${MONDAY_API_KEY}", "-e", "MONDAY_WORKSPACE_NAME=${MONDAY_WORKSPACE_NAME}", "sakce/mcp-server-monday"]}}, "arguments": {"MONDAY_API_KEY": {"description": "API key for authenticating with the Monday.com API.", "required": true, "example": "your-monday-api-key"}, "MONDAY_WORKSPACE_NAME": {"description": "The name of the Monday.com workspace you are working with.", "required": true, "example": "myworkspace"}}}, {"id": "crypto-feargreed-mcp", "name": "crypto-feargreed-mcp", "display_name": "Crypto Fear & Greed Index", "description": "Providing real-time and historical Crypto Fear & Greed Index data.", "repository": {"type": "git", "url": "https://github.com/kukapay/crypto-feargreed-mcp"}, "homepage": "https://github.com/kukapay/crypto-feargreed-mcp", "author": {"name": "KukaPay", "url": "https://github.com/kukapay"}, "license": "MIT", "categories": ["Financial", "Data Analysis", "Cryptocurrency"], "tags": ["Fear & Greed", "Crypto Index", "Analytics"], "examples": [{"title": "Get Current Index", "description": "What is the current Crypto Fear & Greed Index?", "prompt": "What's the current Crypto Fear & Greed Index?"}, {"title": "Analyze Trend", "description": "Show the Fear & Greed Index trend for a specific number of days.", "prompt": "Show me the Crypto Fear & Greed Index trend for the last 30 days."}], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/kukapay/crypto-feargreed-mcp", "main.py"]}}}, {"id": "mcp-local-rag", "name": "mcp-local-rag", "display_name": "Local RAG", "description": "\"primitive\" RAG-like web search model context protocol (MCP) server that runs locally using Google's MediaPipe Text Embedder and DuckDuckGo Search. ✨ no APIs required ✨.", "repository": {"type": "git", "url": "https://github.com/nkapila6/mcp-local-rag"}, "license": "MIT", "author": {"name": "nkapila6"}, "homepage": "https://github.com/nkapila6/mcp-local-rag", "categories": ["Search", "RAG"], "tags": ["RAG", "Search"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--python=3.10", "--from", "git+https://github.com/nkapila6/mcp-local-rag", "mcp-local-rag"]}}}, {"id": "rememberizer-ai", "name": "rememberizer-ai", "display_name": "Rememberizer", "description": "An MCP server designed for interacting with the Rememberizer data source, facilitating enhanced knowledge retrieval.", "repository": {"type": "git", "url": "https://github.com/skydeckai/mcp-server-rememberizer"}, "homepage": "https://github.com/skydeckai/mcp-server-rememberizer", "author": {"name": "skydeckai"}, "license": "MIT", "categories": ["Server"], "tags": ["Rememberizer", "Document Management", "Knowledge Management", "API"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["mcp-server-rememberizer"], "env": {"REMEMBERIZER_API_TOKEN": "${REMEMBERIZER_API_TOKEN}"}}}, "arguments": {"REMEMBERIZER_API_TOKEN": {"description": "Your Rememberizer API token, required for accessing the Rememberizer API.", "required": true, "example": "your_rememberizer_api_token"}}}, {"id": "langflow-doc-qa-server", "name": "langflow-doc-qa-server", "display_name": "Langflow Document Q&A", "description": "A Model Context Protocol server for document Q&A powered by Langflow. It demonstrates core MCP concepts by providing a simple interface to query documents through a Langflow backend.", "repository": {"type": "git", "url": "https://github.com/GongRzhe/Langflow-DOC-QA-SERVER"}, "homepage": "https://github.com/GongRzhe/Langflow-DOC-QA-SERVER", "author": {"name": "GongRzhe"}, "license": "MIT", "categories": ["Q&A", "Document Management"], "tags": ["Langflow", "Document Q&A"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/GongRzhe/Langflow-DOC-QA-SERVER"], "env": {"API_ENDPOINT": "${API_ENDPOINT}"}}}, "arguments": {"API_ENDPOINT": {"description": "The endpoint URL for the Langflow API service.", "required": false, "example": "http://127.0.0.1:7860/api/v1/run/<flow-id>?stream=false"}}}, {"id": "github", "name": "github", "display_name": "GitHub", "description": "MCP Server for the GitHub API, enabling file operations, repository management, search functionality, and more.", "repository": {"type": "git", "url": "https://github.com/modelcontextprotocol/servers"}, "homepage": "https://github.com/modelcontextprotocol/servers/tree/main/src/github#readme", "author": {"name": "MCP Team"}, "license": "MIT", "categories": ["api", "development", "utility"], "tags": ["github", "code", "repository", "git"], "arguments": {"GITHUB_PERSONAL_ACCESS_TOKEN": {"description": "Personal Access Token for GitHub to authenticate API requests", "required": true, "example": "ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"}}, "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "package": "@modelcontextprotocol/server-github", "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_PERSONAL_ACCESS_TOKEN}"}, "description": "Install and run using NPX", "recommended": true}, "docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "--rm", "-e", "GITHUB_PERSONAL_ACCESS_TOKEN", "mcp/github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_PERSONAL_ACCESS_TOKEN}"}, "description": "Run with <PERSON><PERSON>"}}, "examples": [{"title": "Search GitHub repositories", "description": "Find repositories related to machine learning", "prompt": "Find GitHub repositories about machine learning with more than 1000 stars."}, {"title": "View repository contents", "description": "Browse files in a GitHub repository", "prompt": "Show me the main Python files in the Hugging Face transformers repository."}]}, {"id": "qgis", "name": "qgis", "display_name": "QGIS Model Context Protocol Integration", "description": "connects QGIS to Claude AI through the MCP. This integration enables prompt-assisted project creation, layer loading, code execution, and more.", "repository": {"type": "git", "url": "https://github.com/jjsantos01/qgis_mcp"}, "homepage": "https://github.com/jjsantos01/qgis_mcp", "author": {"name": "jjsantos01"}, "license": "MIT", "categories": ["GIS", "AI Integration", "Plugin"], "tags": ["QGIS"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "https://github.com/jjsantos01/qgis_mcp", "src/qgis_mcp/qgis_mcp_server.py"]}}, "examples": [{"title": "Demo Command Sequence", "description": "A series of commands to demonstrate the QGIS MCP integration.", "prompt": "1. <PERSON> to check the connection. If it works, continue with the following steps.\n2. Create a new project and save it at: \"C:/Users/<USER>/GitHub/qgis_mcp/data/cdmx.qgz\"\n3. Load the vector layer: \"C:/Users/<USER>/GitHub/qgis_mcp/data/cdmx/mgpc_2019.shp\" and name it \"Colonias\".\n4. Load the raster layer: \"C:/Users/<USER>/GitHub/qgis_mcp/data/09014.tif\" and name it \"BJ\".\n5. Zoom to the \"BJ\" layer.\n6. Execute the centroid algorithm on the \"Colonias\" layer. Skip the geometry check. Save the output to \"colonias_centroids.geojson\".\n7. Execute code to create a choropleth map using the \"POB2010\" field in the \"Colonias\" layer. Use the quantile classification method with 5 classes and the Spectral color ramp.\n8. Render the map to \"C:/Users/<USER>/GitHub/qgis_mcp/data/cdmx.png\"\n9. Save the project."}]}, {"id": "openapi", "name": "openapi", "display_name": "OpenAPI", "description": "Interact with [OpenAPI](https://www.openapis.org/) APIs.", "repository": {"type": "git", "url": "https://github.com/snaggle-ai/openapi-mcp-server"}, "homepage": "https://github.com/snaggle-ai/openapi-mcp-server", "author": {"name": "snaggle-ai"}, "license": "MIT", "categories": ["API", "OpenAPI"], "tags": ["openapi", "api exploration"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "openapi-mcp-server"]}}, "examples": [{"title": "Finding information about an API", "description": "Ask <PERSON> to find information about specific APIs.", "prompt": "Find information about the Stripe API."}, {"title": "Explaining API usage", "description": "Request explanations on using specific endpoints.", "prompt": "Explain how to use the GitHub API's repository endpoints."}]}, {"id": "salesforce-mcp", "name": "salesforce-mcp", "display_name": "Salesforce Connector", "description": "Interact with Salesforce Data and Metadata", "repository": {"type": "git", "url": "https://github.com/smn2gnt/MCP-Salesforce"}, "license": "[NOT GIVEN]", "author": {"name": "smn2gnt"}, "homepage": "https://github.com/smn2gnt/MCP-Salesforce", "categories": ["Salesforce"], "tags": ["salesforce"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "mcp-salesforce-connector", "salesforce"], "env": {"SALESFORCE_USERNAME": "${SALESFORCE_USERNAME}", "SALESFORCE_PASSWORD": "${SALESFORCE_PASSWORD}", "SALESFORCE_SECURITY_TOKEN": "${SALESFORCE_SECURITY_TOKEN}"}}}, "arguments": {"SALESFORCE_USERNAME": {"description": "Your Salesforce username for authentication", "required": true, "example": "<EMAIL>"}, "SALESFORCE_PASSWORD": {"description": "Your Salesforce password for authentication", "required": true}, "SALESFORCE_SECURITY_TOKEN": {"description": "Your Salesforce security token for additional security measures", "required": true}}}, {"id": "youtube", "name": "youtube", "display_name": "YouTube", "description": "Comprehensive YouTube API integration for video management, Shorts creation, and analytics.", "repository": {"type": "git", "url": "https://github.com/ZubeidHendricks/youtube-mcp-server"}, "homepage": "https://github.com/ZubeidHendricks/youtube-mcp-server", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["youtube", "api"], "tags": ["youtube", "video", "transcripts", "api"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-youtube"], "env": {"YOUTUBE_API_KEY": "${YOUTUBE_API_KEY}"}}}, "examples": [{"title": "Managing Videos", "description": "Example code for managing videos using the YouTube MCP Server.", "prompt": "// Get video details\nconst video = await youtube.videos.getVideo({\n  videoId: \"video-id\"\n});"}], "arguments": {"YOUTUBE_API_KEY": {"description": "Your YouTube Data API key, needed for authentication when making requests to the YouTube API.", "required": true, "example": "AIzaSyD4-1234abcdEFGHijklmnop"}}}, {"id": "scrapling-fetch", "name": "scrapling-fetch", "display_name": "Scrapling <PERSON>", "description": "Access text content from bot-protected websites. Fetches HTML/markdown from sites with anti-automation measures using Scrapling.", "repository": {"type": "git", "url": "https://github.com/cyberchitta/scrapling-fetch-mcp"}, "license": "Apache 2", "author": {"name": "cyberchitta"}, "homepage": "https://github.com/cyberchitta/scrapling-fetch-mcp", "categories": ["Web"], "tags": ["scrapling", "fetch"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["scrapling-fetch-mcp"]}}}, {"id": "holaspirit", "name": "holaspirit", "display_name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Interact with [<PERSON><PERSON><PERSON><PERSON>](https://www.holaspirit.com/).", "repository": {"type": "git", "url": "https://github.com/syucream/holaspirit-mcp-server"}, "homepage": "https://github.com/syucream/holaspirit-mcp-server", "author": {"name": "syucream"}, "license": "MIT", "categories": ["API"], "tags": ["<PERSON><PERSON><PERSON><PERSON>", "AI"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "holaspirit-mcp-server"], "env": {"HOLASPIRIT_API_TOKEN": "${HOLASPIRIT_API_TOKEN}"}}}, "arguments": {"HOLASPIRIT_API_TOKEN": {"description": "Your Holaspirit API token", "required": true, "example": "<your token>"}}}, {"id": "rag-web-browser", "name": "rag-web-browser", "display_name": "RAG Web Browser Server", "description": "An MCP server for Apify's open-source RAG Web Browser [Actor](https://apify.com/apify/rag-web-browser) to perform web searches, scrape URLs, and return content in Markdown.", "repository": {"type": "git", "url": "https://github.com/apify/mcp-server-rag-web-browser"}, "homepage": "https://github.com/apify/mcp-server-rag-web-browser", "author": {"name": "apify"}, "license": "MIT", "categories": ["Web Development", "AI"], "tags": ["RAG", "Web Browser", "AI Agents"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["@apify/mcp-server-rag-web-browser"], "env": {"APIFY_TOKEN": "${APIFY_TOKEN}"}}}, "examples": [{"title": "Web Search Example", "description": "Ask the server to perform a web search for a specific query.", "prompt": "What is an MCP server and how can it be used?"}, {"title": "Research Papers Query", "description": "Find and analyze recent research papers about LLMs.", "prompt": "Find and analyze recent research papers about LLMs."}], "arguments": {"APIFY_TOKEN": {"description": "Environment variable for your Apify API token to authenticate requests.", "required": true, "example": "your-apify-api-token"}}}, {"id": "aws-kb-retrieval", "name": "aws-kb-retrieval", "display_name": "AWS Knowledge Base Retrieval", "description": "Retrieval from AWS Knowledge Base using Bedrock Agent Runtime", "repository": {"type": "git", "url": "https://github.com/modelcontextprotocol/servers"}, "homepage": "https://github.com/modelcontextprotocol/servers/blob/main/src/aws-kb-retrieval-server", "author": {"name": "modelcontextprotocol"}, "license": "MIT", "categories": ["AWS", "Knowledge Base"], "tags": ["Knowledge Base", "Retrieval", "AWS", "Bedrock Agent Runtime"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-aws-kb-retrieval"], "env": {"AWS_ACCESS_KEY_ID": "${AWS_ACCESS_KEY_ID}", "AWS_SECRET_ACCESS_KEY": "${AWS_SECRET_ACCESS_KEY}", "AWS_REGION": "${AWS_REGION}"}}, "docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "--rm", "-e", "AWS_ACCESS_KEY_ID", "-e", "AWS_SECRET_ACCESS_KEY", "-e", "AWS_REGION", "mcp/aws-kb-retrieval-server"], "env": {"AWS_ACCESS_KEY_ID": "${AWS_ACCESS_KEY_ID}", "AWS_SECRET_ACCESS_KEY": "${AWS_SECRET_ACCESS_KEY}", "AWS_REGION": "${AWS_REGION}"}}}, "arguments": {"AWS_ACCESS_KEY_ID": {"description": "The access key ID for your AWS account used for authentication.", "required": true, "example": "YOUR_ACCESS_KEY_HERE"}, "AWS_SECRET_ACCESS_KEY": {"description": "The secret access key for your AWS account used for authentication.", "required": true, "example": "YOUR_SECRET_ACCESS_KEY_HERE"}, "AWS_REGION": {"description": "The AWS region where your resources are located.", "required": true, "example": "us-east-1"}}}, {"id": "xiyan-mcp-server", "name": "xiyan-mcp-server", "display_name": "XiYan MCP Server", "description": "An MCP server that supports fetching data from a database using natural language queries, powered by XiyanSQL as the text-to-SQL LLM.", "repository": {"type": "git", "url": "https://github.com/XGenerationLab/xiyan_mcp_server"}, "homepage": "https://github.com/XGenerationLab/xiyan_mcp_server", "author": {"name": "XGenerationLab"}, "license": "Apache-2.0", "categories": ["Database"], "tags": ["database", "sql", "database"], "installations": {"python": {"type": "python", "command": "python", "args": ["-m", "xiyan_mcp_server"], "env": {"YML": "${YML}"}}}, "examples": [{"title": "Installing from pip", "description": "You can install the server through pip, and it will install the latest version.", "prompt": "pip install xiyan-mcp-server"}, {"title": "Running the server", "description": "After installing, you can run the server using a yml configuration file.", "prompt": "env YML=path/to/yml python -m xiyan_mcp_server"}], "arguments": {"YML": {"description": "The path to the YAML configuration file required for setting up the server environment variables.", "required": true, "example": "path/to/yml"}}}, {"id": "terminal-control", "name": "terminal-control", "display_name": "Terminal Controller", "description": "A MCP server that enables secure terminal command execution, directory navigation, and file system operations through a standardized interface.", "repository": {"type": "git", "url": "https://github.com/GongRzhe/terminal-controller-mcp"}, "homepage": "https://github.com/GongRzhe/terminal-controller-mcp", "author": {"name": "GongRzhe"}, "license": "MIT", "categories": ["Utility", "Terminal"], "tags": ["terminal", "command execution", "file management", "cross-platform"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["terminal-controller"]}, "python": {"type": "python", "command": "python", "args": ["-m", "terminal_controller"]}}, "examples": [{"title": "Run Command Example", "description": "Run the command `ls -la` in the current directory", "prompt": "Run the command `ls -la` in the current directory"}, {"title": "Navigate Directory Example", "description": "Navigate to my Documents folder", "prompt": "Navigate to my Documents folder"}, {"title": "Show Downloads Example", "description": "Show me the contents of my Downloads directory", "prompt": "Show me the contents of my Downloads directory"}, {"title": "Recent Commands Example", "description": "Show me my recent command history", "prompt": "Show me my recent command history"}], "arguments": {"terminal_controller": {"description": "The Python module that contains the implementation of the Terminal Controller's functionalities.", "required": true, "example": "terminal_controller"}}}, {"id": "tavily-search", "name": "tavily-search", "display_name": "<PERSON><PERSON>", "description": "An MCP server for Tavily's search & news API, with explicit site inclusions/exclusions", "repository": {"type": "git", "url": "https://github.com/RamXX/mcp-tavily"}, "homepage": "https://github.com/RamXX/mcp-tavily", "author": {"name": "RamXX"}, "license": "MIT", "categories": ["AI", "Web Search", "Development"], "tags": ["AI", "Search"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["mcp_server_tavily"], "env": {"TAVILY_API_KEY": "your_api_key_here"}}, "python": {"type": "python", "command": "python", "args": ["-m", "mcp_server_tavily"], "env": {"TAVILY_API_KEY": "your_api_key_here"}}}, "examples": [{"title": "Regular Web Search", "description": "Perform a standard web search using <PERSON><PERSON>'s capabilities.", "prompt": "Tell me about <PERSON><PERSON><PERSON>'s newly released MCP protocol"}, {"title": "Domain Filtering Report", "description": "Generate a report filtering specific domains.", "prompt": "Tell me about redwood trees. Please use MLA format in markdown syntax and include the URLs in the citations. Exclude Wikipedia sources."}, {"title": "Direct Answer Search", "description": "Use answer search mode for getting direct answers.", "prompt": "I want a concrete answer backed by current web sources: What is the average lifespan of redwood trees?"}, {"title": "News Search", "description": "Retrieve recent news articles on specific topics.", "prompt": "Give me the top 10 AI-related news in the last 5 days."}], "arguments": {"TAVILY_API_KEY": {"description": "Your Tavily API key for accessing Tavily's search API functionalities.", "required": true, "example": "your_api_key_here"}}}, {"id": "gmail", "name": "gmail", "display_name": "Gmail AutoAuth", "description": "A Model Context Protocol (MCP) server for Gmail integration in Claude Desktop with auto authentication support.", "repository": {"type": "git", "url": "https://github.com/GongRzhe/Gmail-MCP-Server"}, "homepage": "https://github.com/GongRzhe/Gmail-MCP-Server", "author": {"name": "GongRzhe"}, "license": "MIT", "categories": ["Email", "Gmail"], "tags": ["gmail", "autoauth", "claude"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["@gongrzhe/server-gmail-autoauth-mcp"]}, "docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "--rm", "-v", "mcp-gmail:/gmail-server", "-e", "${GMAIL_CREDENTIALS_PATH}=/gmail-server/credentials.json", "mcp/gmail"]}}, "arguments": {"GMAIL_CREDENTIALS_PATH": {"description": "The path to the Gmail credentials file that the server will use for OAuth authentication.", "required": true, "example": "/gmail-server/credentials.json"}}}, {"id": "kubernetes-and-openshift", "name": "kubernetes-and-openshift", "display_name": "Kubernetes and OpenShift", "description": "A powerful Kubernetes MCP server with additional support for OpenShift. Besides providing CRUD operations for any Kubernetes resource, this server provides specialized tools to interact with your cluster.", "repository": {"type": "git", "url": "https://github.com/manusa/kubernetes-mcp-server"}, "homepage": "https://github.com/manusa/kubernetes-mcp-server", "author": {"name": "manusa"}, "license": "MIT", "categories": ["Kubernetes", "Server"], "tags": ["Kubernetes", "Server"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "kubernetes-mcp-server@latest"]}}}, {"id": "mysql", "name": "mysql", "display_name": "MySQL Database Integration", "description": "MySQL database integration in Python with configurable access controls and schema inspection", "repository": {"type": "git", "url": "https://github.com/designcomputer/mysql_mcp_server"}, "homepage": "https://github.com/designcomputer/mysql_mcp_server", "author": {"name": "designcomputer"}, "license": "MIT", "categories": ["Database", "MySQL"], "tags": ["MySQL", "Database Access"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["mysql_mcp_server"], "env": {"MYSQL_HOST": "${MYSQL_HOST}", "MYSQL_PORT": "${MYSQL_PORT}", "MYSQL_USER": "${MYSQL_USER}", "MYSQL_PASSWORD": "${MYSQL_PASSWORD}", "MYSQL_DATABASE": "${MYSQL_DATABASE}"}}}, "arguments": {"MYSQL_HOST": {"description": "Database host", "required": true, "example": "localhost"}, "MYSQL_PORT": {"description": "Database port (defaults to 3306 if not specified)", "required": false, "example": "3306"}, "MYSQL_USER": {"description": "Username for database access", "required": true, "example": "your_username"}, "MYSQL_PASSWORD": {"description": "Password for the database user", "required": true, "example": "your_password"}, "MYSQL_DATABASE": {"description": "Database name to connect to", "required": true, "example": "your_database"}}}, {"id": "mindmap", "name": "mindmap", "display_name": "Mindmap", "description": "A server that generates mindmaps from input containing markdown code.", "repository": {"type": "git", "url": "https://github.com/YuChenSSR/mindmap-mcp-server"}, "homepage": "https://github.com/YuChenSSR/mindmap-mcp-server", "author": {"name": "YuChenSSR"}, "license": "MIT", "categories": ["<PERSON><PERSON>", "Mindmap"], "tags": ["mindmap", "markdown", "interactive"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["mindmap-mcp-server", "--return-type", "html"]}, "docker": {"type": "docker", "command": "docker", "args": ["run", "--rm", "-i", "-v", "/path/to/output/folder:/output", "ychen94/mindmap-converter-mcp:latest"]}}, "examples": [{"title": "Basic Mindmap Generation", "description": "Generate a mindmap from <PERSON><PERSON> input.", "prompt": "give a mindmap for the following markdown code, using a mindmap tool:\n```\n# Project Planning\n## Research\n### Market Analysis\n### Competitor Review\n## Design\n### Wireframes\n### Mockups\n## Development\n### Frontend\n### Backend\n## Testing\n### Unit Tests\n### User Testing\n```\n"}, {"title": "Save Mindmap to File", "description": "Save the generated mindmap as an HTML file and open it in the browser.", "prompt": "give a mindmap for the following markdown input_code using a mindmap tool,\nafter that,use iterm to open the generated html file.\ninput_code:\n```\nmarkdown content\n```\n"}, {"title": "Elephant in Refrigerator Mindmap", "description": "Create a mindmap about the process of putting an elephant into a refrigerator.", "prompt": "Think about the process of putting an elephant into a refrigerator, and provide a mind map. Open it with a terminal."}]}, {"id": "travel-planner", "name": "travel-planner", "display_name": "Travel Planner", "description": "Travel planning and itinerary management server integrating with Google Maps API for location search, place details, and route calculations.", "repository": {"type": "git", "url": "https://github.com/GongRzhe/TRAVEL-PLANNER-MCP-Server"}, "homepage": "https://github.com/GongRzhe/TRAVEL-PLANNER-MCP-Server", "author": {"name": "GongRzhe"}, "license": "MIT", "categories": ["travel"], "tags": ["google-maps", "travel-planning"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["@gongrzhe/server-travelplanner-mcp"], "env": {"GOOGLE_MAPS_API_KEY": "${GOOGLE_MAPS_API_KEY}"}}}, "examples": [{"title": "Search Places", "description": "Search for places using Google Places API", "prompt": "searchPlaces({ query: 'restaurants', location: '34.0522,-118.2437', radius: 5000 });"}, {"title": "Get Place Details", "description": "Get detailed information about a specific place", "prompt": "getPlaceDetails({ placeId: 'ChIJN1t_tDeuEmsRUcIa02j2sDE' });"}, {"title": "Calculate Route", "description": "Calculate route between two locations", "prompt": "calculateRoute({ origin: 'Los Angeles, CA', destination: 'San Francisco, CA', mode: 'driving' });"}, {"title": "Get Time Zone", "description": "Get timezone information for a location", "prompt": "getTimeZone({ location: '34.0522,-118.2437' });"}], "arguments": {"GOOGLE_MAPS_API_KEY": {"description": "Your Google Maps API key with the following APIs enabled: Places API, Directions API, Geocoding API, Time Zone API", "required": true, "example": "your_google_maps_api_key"}}}, {"id": "postgresql", "name": "postgresql", "display_name": "PostgreSQL", "description": "Read-only database access with schema inspection", "repository": {"type": "git", "url": "https://github.com/modelcontextprotocol/servers"}, "license": "MIT", "categories": ["Database"], "tags": ["PostgreSQL", "Database", "Read-Only"], "author": {"name": "modelcontextprotocol"}, "homepage": "https://github.com/modelcontextprotocol/servers/blob/main/src/postgres", "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://localhost/mydb"]}, "docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "--rm", "mcp/postgres", "postgresql://host.docker.internal:5432/mydb"]}}}, {"id": "todoist", "name": "todoist", "display_name": "Todoist", "description": "Interact with <PERSON><PERSON><PERSON> to manage your tasks.", "repository": {"type": "git", "url": "https://github.com/abhiz123/todoist-mcp-server"}, "homepage": "https://github.com/abhiz123/todoist-mcp-server", "author": {"name": "abhiz123"}, "license": "MIT", "categories": ["Productivity", "TODO List"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@abhiz123/todoist-mcp-server"], "env": {"TODOIST_API_TOKEN": "${TODOIST_API_TOKEN}"}}}, "tags": ["task management", "todoist", "natural language processing"], "examples": [{"title": "Creating Tasks", "description": "Example commands for creating tasks", "prompt": "\"Create task 'Team Meeting'\""}, {"title": "Getting Tasks", "description": "Example commands for retrieving tasks", "prompt": "\"Show all my tasks\""}, {"title": "Updating Tasks", "description": "Example commands for updating tasks", "prompt": "\"Update documentation task to be due next week\""}, {"title": "Completing Tasks", "description": "Example commands for completing tasks", "prompt": "\"Mark the PR review task as complete\""}, {"title": "Deleting Tasks", "description": "Example commands for deleting tasks", "prompt": "\"Delete the PR review task\""}], "arguments": {"TODOIST_API_TOKEN": {"description": "API token to authenticate with the Todoist service", "required": true, "example": "your_api_token_here"}}}, {"id": "ntfy-mcp", "name": "ntfy-mcp", "display_name": "Your Friendly Task Completion Notifier", "description": "The MCP server that keeps you informed by sending the notification on phone using ntfy", "repository": {"type": "git", "url": "https://github.com/teddyzxcv/ntfy-mcp"}, "homepage": "https://github.com/teddyzxcv/ntfy-mcp", "author": {"name": "teddyzxcv"}, "license": "Apache License 2.0", "categories": ["Notification", "Productivity"], "tags": ["ntfy", "notifications"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/teddyzxcv/ntfy-mcp"], "env": {"NTFY_TOPIC": "${NTFY_TOPIC}"}}}, "examples": [{"title": "Python Hello World", "description": "Write a prompt to execute a task and receive a notification upon completion.", "prompt": "Write me a hello world in python, notify me when the task is done"}], "arguments": {"NTFY_TOPIC": {"description": "Environment variable representing the topic name for notifications to be sent to.", "required": true, "example": "your_topic_name"}}}, {"id": "everart", "name": "everart", "display_name": "EverArt", "description": "AI image generation using various models", "repository": {"type": "git", "url": "https://github.com/modelcontextprotocol/servers"}, "homepage": "https://github.com/modelcontextprotocol/servers/blob/main/src/everart", "author": {"name": "modelcontextprotocol"}, "license": "[NOT GIVEN]", "categories": ["image generation"], "tags": ["EverArt", "API", "<PERSON>"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-everart"], "env": {"EVERART_API_KEY": "${EVERART_API_KEY}"}}, "docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "--rm", "-e", "EVERART_API_KEY", "mcp/everart"], "env": {"EVERART_API_KEY": "${EVERART_API_KEY}"}}}, "arguments": {"EVERART_API_KEY": {"description": "API key to access the EverArt API", "required": true, "example": "your_key_here"}}}, {"id": "pushover", "name": "pushover", "display_name": "Pushover Notifications", "description": "Send instant notifications to your devices using [Pushover.net](https://pushover.net/)", "repository": {"type": "git", "url": "https://github.com/ashiknesin/pushover-mcp"}, "homepage": "https://github.com/ashiknesin/pushover-mcp", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["notification"], "tags": ["pushover", "notifications"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "pushover-mcp@latest", "start", "--token", "${YOUR_TOKEN}", "--user", "${YOUR_USER}"]}}, "arguments": {"YOUR_TOKEN": {"description": "Application token required for authenticating with Pushover.net", "required": true, "example": "abcdef123456"}, "YOUR_USER": {"description": "User key associated with your Pushover.net account", "required": true, "example": "**********:abcdef123456"}}}, {"id": "memory", "name": "memory", "display_name": "Knowledge Graph Memory", "description": "Knowledge graph-based persistent memory system", "repository": {"type": "git", "url": "https://github.com/modelcontextprotocol/servers"}, "homepage": "https://github.com/modelcontextprotocol/servers/blob/main/src/memory", "author": {"name": "modelcontextprotocol"}, "license": "MIT", "categories": ["memory", "knowledge", "server"], "tags": ["knowledge graph", "memory", "persistent memory"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "-v", "claude-memory:/app/dist", "--rm", "mcp/memory"]}}, "examples": [{"title": "Basic Memory Interaction", "description": "A simple interaction with memory where user details are remembered.", "prompt": "Remembering..."}], "arguments": {"MEMORY_FILE_PATH": {"description": "Path to the memory storage JSON file (default: memory.json in the server directory)", "required": false, "example": "/path/to/custom/memory.json"}}}, {"id": "elevenlabs", "name": "elevenlabs", "display_name": "ElevenLabs", "description": "A server that integrates with ElevenLabs text-to-speech API capable of generating full voiceovers with multiple voices.", "repository": {"type": "git", "url": "https://github.com/mamertofabian/elevenlabs-mcp-server"}, "homepage": "https://github.com/mamertofabian/elevenlabs-mcp-server", "author": {"name": "mamertofabian"}, "license": "MIT", "categories": ["Text-to-Speech", "Speech Synthesis"], "tags": ["ElevenLabs", "Text-to-Speech", "SvelteKit", "TTS"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["elevenlabs-mcp-server"], "env": {"ELEVENLABS_API_KEY": "${ELEVENLABS_API_KEY}", "ELEVENLABS_VOICE_ID": "${ELEVENLABS_VOICE_ID}", "ELEVENLABS_MODEL_ID": "${ELEVENLABS_MODEL_ID}", "ELEVENLABS_STABILITY": "${ELEVENLABS_STABILITY}", "ELEVENLABS_SIMILARITY_BOOST": "${ELEVENLABS_SIMILARITY_BOOST}", "ELEVENLABS_STYLE": "${ELEVENLABS_STYLE}", "ELEVENLABS_OUTPUT_DIR": "${ELEVENLABS_OUTPUT_DIR}"}}}, "arguments": {"ELEVENLABS_API_KEY": {"description": "Your API key for ElevenLabs to access the text-to-speech services.", "required": true, "example": "sk-12345abcd"}, "ELEVENLABS_VOICE_ID": {"description": "The ID of the voice you want to use for synthesis.", "required": true, "example": "voice-12345"}, "ELEVENLABS_MODEL_ID": {"description": "The model ID to be used, indicating the version of the ElevenLabs API to utilize.", "required": false, "example": "eleven_flash_v2"}, "ELEVENLABS_STABILITY": {"description": "Stability of the voice generation; controls variations in the output voice.", "required": false, "example": "0.5"}, "ELEVENLABS_SIMILARITY_BOOST": {"description": "Boosting similarity for the voices; affects how closely the output mimics the selected voice.", "required": false, "example": "0.75"}, "ELEVENLABS_STYLE": {"description": "Style parameter to adjust the expression in the generated speech.", "required": false, "example": "0.1"}, "ELEVENLABS_OUTPUT_DIR": {"description": "Directory path where the generated audio files will be saved.", "required": false, "example": "output"}}}, {"id": "airbnb", "name": "airbnb", "display_name": "Airbnb", "description": "Provides tools to search Airbnb and get listing details.", "repository": {"type": "git", "url": "https://github.com/openbnb-org/mcp-server-airbnb"}, "homepage": "https://github.com/openbnb-org/mcp-server-airbnb", "author": {"name": "openbnb-org"}, "license": "MIT", "categories": ["Travel", "Accommodation"], "tags": ["Airbnb", "search", "listings"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@openbnb/mcp-server-airbnb"], "description": "Run with npx (requires npm install)"}}, "examples": [{"title": "Search for Airbnb Listings", "description": "Search for listings in a specified location.", "prompt": "Search for listings in New York"}, {"title": "Get Listing Details", "description": "Retrieve details for a specific listing.", "prompt": "Get details for listing 12345"}], "arguments": {"location": {"description": "The location where you want to search for Airbnb listings", "required": true, "example": "New York City"}, "placeId": {"description": "The unique identifier for a specific place or location", "required": false, "example": "ChIJN1t_tDeuEmsRUsoyG83frY4"}, "checkin": {"description": "The check-in date for your stay in YYYY-MM-DD format", "required": false, "example": "2023-10-01"}, "checkout": {"description": "The check-out date for your stay in YYYY-MM-DD format", "required": false, "example": "2023-10-05"}, "adults": {"description": "The number of adults staying", "required": false, "example": "2"}, "children": {"description": "The number of children staying", "required": false, "example": "1"}, "infants": {"description": "The number of infants staying", "required": false, "example": "1"}, "pets": {"description": "The number of pets allowed in the listing", "required": false, "example": "2"}, "minPrice": {"description": "The minimum price per night for the listings", "required": false, "example": "50"}, "maxPrice": {"description": "The maximum price per night for the listings", "required": false, "example": "300"}, "cursor": {"description": "A cursor for paginating through results", "required": false, "example": "next-page-token"}, "ignoreRobotsText": {"description": "Set to true to disregard Airbnb's robots.txt rules for all requests", "required": false, "example": "true"}}}, {"id": "prometheus", "name": "prometheus", "display_name": "Prometheus", "description": "Query and analyze Prometheus - open-source monitoring system.", "repository": {"type": "git", "url": "https://github.com/pab1it0/prometheus-mcp-server"}, "homepage": "https://github.com/pab1it0/prometheus-mcp-server", "author": {"name": "pab1it0"}, "license": "MIT", "categories": ["Query", "Discovery"], "tags": ["Prometheus", "Metrics", "AI"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "https://github.com/pab1it0/prometheus-mcp-server", "src/prometheus_mcp_server/main.py"], "env": {"PROMETHEUS_URL": "${PROMETHEUS_URL}", "PROMETHEUS_USERNAME": "${PROMETHEUS_USERNAME}", "PROMETHEUS_PASSWORD": "${PROMETHEUS_PASSWORD}"}}}, "examples": [{"title": "Execute Query", "description": "Execute a PromQL instant query against Prometheus", "prompt": "execute_query({ query: \"up\" })"}, {"title": "List Metrics", "description": "Get a list of metrics from Prometheus", "prompt": "list_metrics()"}], "arguments": {"PROMETHEUS_URL": {"description": "The URL of the Prometheus server you want to connect to.", "required": true, "example": "http://your-prometheus-server:9090"}, "PROMETHEUS_USERNAME": {"description": "The username for basic authentication when accessing the Prometheus server.", "required": false, "example": "your_username"}, "PROMETHEUS_PASSWORD": {"description": "The password for basic authentication when accessing the Prometheus server.", "required": false, "example": "your_password"}}}, {"id": "searxng", "name": "searxng", "display_name": "SearXNG", "description": "A Model Context Protocol Server for [SearXNG](https://docs.searxng.org/)", "repository": {"type": "git", "url": "https://github.com/ihor-sokoliuk/mcp-searxng"}, "homepage": "https://github.com/ihor-sokoliuk/mcp-searxng", "author": {"name": "<PERSON>hor-<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["web"], "tags": ["search", "searxng", "api"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/ihor-sokoliuk/mcp-searxng"], "env": {"SEARXNG_URL": "${SEARXNG_URL}"}}}, "arguments": {"SEARXNG_URL": {"description": "Environment variable to set the URL of the SearXNG instance that will be used for search queries.", "required": true, "example": "http://localhost:8080"}}}, {"id": "pinecone", "name": "pinecone", "display_name": "Pinecone Model Context Protocol for Claude Desktop", "description": "MCP server for searching and uploading records to Pinecone. Allows for simple RAG features, leveraging Pinecone's Inference API.", "repository": {"type": "git", "url": "https://github.com/sirmews/mcp-pinecone"}, "homepage": "https://github.com/sirmews/mcp-pinecone", "author": {"name": "sirme<PERSON>"}, "license": "MIT", "categories": ["Pinecone"], "tags": ["pinecone"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--index-name", "${your-index-name}", "--api-key", "${your-secret-api-key}", "mcp-pinecone"]}}}, {"id": "atlassian", "name": "atlassian", "display_name": "Atlassian", "description": "Interact with Atlassian Cloud products (Confluence and Jira) including searching/reading Confluence spaces/pages, accessing Jira issues, and project metadata.", "repository": {"type": "git", "url": "https://github.com/sooperset/mcp-atlassian"}, "homepage": "https://github.com/sooperset/mcp-atlassian", "author": {"name": "sooperset"}, "license": "MIT", "categories": ["Integration", "Productivity"], "tags": ["Atlassian", "Confluence", "<PERSON><PERSON>"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["mcp-atlassian", "--confluence-url=${CONFLUENCE_URL}", "--confluence-username=${CONFLUENCE_USERNAME}", "--confluence-token=${CONFLUENCE_TOKEN}", "--jira-url=${JIRA_URL}", "--jira-username=${JIRA_USERNAME}", "--jira-token=${JIRA_TOKEN}"], "description": "Run with uvx (requires uv install)"}, "python": {"type": "python", "command": "python", "args": ["-m", "mcp-atlassian", "--confluence-url=${CONFLUENCE_URL}", "--confluence-username=${CONFLUENCE_USERNAME}", "--confluence-token=${CONFLUENCE_TOKEN}", "--jira-url=${JIRA_URL}", "--jira-username=${JIRA_USERNAME}", "--jira-token=${JIRA_TOKEN}"], "description": "Run with Python module (requires pip install)"}, "docker": {"type": "docker", "command": "docker", "args": ["run", "--rm", "-i", "mcp/atlassian", "--confluence-url=${CONFLUENCE_URL}", "--confluence-username=${CONFLUENCE_USERNAME}", "--confluence-token=${CONFLUENCE_TOKEN}", "--jira-url=${JIRA_URL}", "--jira-username=${JIRA_USERNAME}", "--jira-token=${JIRA_TOKEN}"]}}, "arguments": {"CONFLUENCE_URL": {"description": "The URL of the Confluence site to connect to. Required for both Cloud and Server/Data Center deployments.", "required": true, "example": "https://your-company.atlassian.net/wiki or https://confluence.your-company.com"}, "CONFLUENCE_USERNAME": {"description": "The username for the Confluence account (email for Cloud). Required to authenticate with Confluence.", "required": true, "example": "<EMAIL>"}, "CONFLUENCE_TOKEN": {"description": "The API token or personal access token for the Confluence account. Required for authentication with Confluence.", "required": true, "example": "your_api_token or your_token"}, "JIRA_URL": {"description": "The URL of the Jira site to connect to. Required for both Cloud and Server/Data Center deployments.", "required": true, "example": "https://your-company.atlassian.net or https://jira.your-company.com"}, "JIRA_USERNAME": {"description": "The username for the <PERSON>ra account (email for Cloud). Required to authenticate with <PERSON><PERSON>.", "required": true, "example": "<EMAIL>"}, "JIRA_TOKEN": {"description": "The API token or personal access token for the Jira account. Required for authentication with Jira.", "required": true, "example": "your_api_token or your_token"}}}, {"id": "open-strategy-partners-marketing-tools", "name": "open-strategy-partners-marketing-tools", "display_name": "Open Strategy Partners Marketing Tools", "description": "Content editing codes, value map, and positioning tools for product marketing.", "repository": {"type": "git", "url": "https://github.com/open-strategy-partners/osp_marketing_tools"}, "homepage": "https://github.com/open-strategy-partners/osp_marketing_tools", "author": {"name": "open-strategy-partners"}, "license": "CC-BY-SA-4.0", "categories": ["Marketing", "Content Creation", "SEO"], "tags": ["LLM", "Technical Writing", "Optimization"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/open-strategy-partners/osp_marketing_tools@main", "osp_marketing_tools"]}}, "examples": [{"title": "Value Map Generation", "description": "Generate an OSP value map for a product with specified features for a target audience.", "prompt": "Generate an OSP value map for CloudDeploy, focusing on DevOps engineers with these key features: - Automated deployment pipeline - Infrastructure as code support - Real-time monitoring - Multi-cloud compatibility."}, {"title": "Meta Information Creation", "description": "Create optimized metadata for an article based on a specific topic and audience.", "prompt": "Use the OSP meta tool to generate metadata for an article about containerization best practices. Primary keyword: 'Docker containers', audience: system administrators, content type: technical guide."}, {"title": "Content Editing", "description": "Review technical content using OSP editing codes for improvements.", "prompt": "Review this technical content using OSP editing codes: Kubernetes helps you manage containers. It's really good at what it does. You can use it to deploy your apps and make them run better."}, {"title": "Technical Writing", "description": "Apply the OSP writing guide to create a document for a specific audience.", "prompt": "Apply the OSP writing guide to create a tutorial about setting up a CI/CD pipeline for junior developers."}]}, {"id": "mongodb-lens", "name": "mongodb-lens", "display_name": "MongoDB Lens", "description": "Full Featured MCP Server for MongoDB Databases.", "repository": {"type": "git", "url": "https://github.com/furey/mongodb-lens"}, "homepage": "https://github.com/furey/mongodb-lens", "author": {"name": "furey"}, "license": "MIT", "categories": ["database"], "tags": ["mongodb", "server"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "mongodb-lens@latest", "mongodb://your-connection-string"], "env": {"CONFIG_LOG_LEVEL": "${CONFIG_LOG_LEVEL}"}}, "docker": {"type": "docker", "command": "docker", "args": ["run", "--rm", "-i", "--network=host", "--pull=always", "-e", "CONFIG_LOG_LEVEL='verbose'", "furey/mongodb-lens", "mongodb://your-connection-string"], "env": {"CONFIG_LOG_LEVEL": "${CONFIG_LOG_LEVEL}"}}}, "arguments": {"CONFIG_LOG_LEVEL": {"description": "Sets the logging level of MongoDB Lens, controlling the verbosity of log output.", "required": false, "example": "verbose"}}}, {"id": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "display_name": "<PERSON><PERSON><PERSON>", "description": "An MCP server to integrate with DevRev APIs to search through your DevRev Knowledge Graph where objects can be imported from diff. sources listed [here](https://devrev.ai/docs/import#available-sources).", "repository": {"type": "git", "url": "https://github.com/kpsunil97/devrev-mcp-server"}, "homepage": "https://github.com/kpsunil97/devrev-mcp-server", "author": {"name": "kpsunil97"}, "license": "MIT", "categories": ["<PERSON><PERSON><PERSON>", "Server"], "tags": ["<PERSON><PERSON><PERSON>", "server", "search"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["devrev-mcp"], "env": {"DEVREV_API_KEY": "${DEVREV_API_KEY}"}}}, "arguments": {"DEVREV_API_KEY": {"description": "Your DevRev API key required to authenticate requests to the DevRev API.", "required": true, "example": "YOUR_DEVREV_API_KEY"}}}, {"id": "eunomia", "name": "eunomia", "display_name": "Eunomia", "description": "Extension of the Eunomia framework that connects Eunomia instruments with MCP servers", "repository": {"type": "git", "url": "https://github.com/whataboutyou-ai/eunomia-MCP-server"}, "homepage": "https://github.com/whataboutyou-ai/eunomia-MCP-server", "author": {"name": "whataboutyou-ai"}, "license": "Apache-2.0", "categories": ["Data Governance"], "tags": ["Eunomia", "Data Governance"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/whataboutyou-ai/eunomia-MCP-server", "orchestra_server"]}}, "arguments": {"APP_NAME": {"description": "Name of the application", "required": true, "example": "mcp-server_orchestra"}, "APP_VERSION": {"description": "Current version of the application", "required": true, "example": "0.1.0"}, "LOG_LEVEL": {"description": "Logging level to control the verbosity of logs (default: 'info')", "required": false, "example": "info"}, "REQUEST_TIMEOUT": {"description": "Environment variable that sets the request timeout duration in seconds", "required": false, "example": "30"}}}, {"id": "google-custom-search", "name": "google-custom-search", "display_name": "Google Custom Search", "description": "Provides Google Search results via the Google Custom Search API", "repository": {"type": "git", "url": "https://github.com/adenot/mcp-google-search"}, "homepage": "https://github.com/adenot/mcp-google-search", "author": {"name": "adenot"}, "license": "MIT", "categories": ["Search Tools", "Web Tools"], "tags": ["Google", "Custom Search", "Webpage Reading"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@adenot/mcp-google-search"], "env": {"GOOGLE_API_KEY": "your-api-key-here", "GOOGLE_SEARCH_ENGINE_ID": "your-search-engine-id-here"}}}, "examples": [{"title": "Search Tool", "description": "Perform web searches using Google Custom Search API.", "prompt": "{\"name\":\"search\",\"arguments\":{\"query\":\"your search query\",\"num\":5}}"}, {"title": "Webpage Reader Tool", "description": "Extract content from any webpage.", "prompt": "{\"name\":\"read_webpage\",\"arguments\":{\"url\":\"https://example.com\"}}"}], "arguments": {"GOOGLE_API_KEY": {"description": "Your Google API key for accessing the Google Custom Search API.", "required": true, "example": "AIzaSyA-xxxxxxxxxxxxxxxxxxxxxxxxxxxx"}, "GOOGLE_SEARCH_ENGINE_ID": {"description": "The unique identifier for your Custom Search Engine that you created on Google.", "required": true, "example": "0********************:abcdefghijk"}}}, {"id": "big<PERSON>y", "name": "big<PERSON>y", "display_name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Server implementation for Google BigQuery integration that enables direct BigQuery database access and querying capabilities", "repository": {"type": "git", "url": "https://github.com/ergut/mcp-bigquery-server"}, "homepage": "https://github.com/ergut/mcp-bigquery-server", "author": {"name": "ergut"}, "license": "MIT", "categories": ["Databases", "AI"], "tags": ["<PERSON><PERSON><PERSON><PERSON>", "AI", "LLM"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@ergut/mcp-bigquery-server", "--project-id", "${PROJECT_ID}", "--location", "${LOCATION}"]}}, "arguments": {"PROJECT_ID": {"description": "Your Google Cloud project ID", "required": true, "example": "your-project-id"}, "LOCATION": {"description": "BigQuery location, defaults to 'us-central1'.", "required": false, "example": "us-central1"}}}, {"id": "bitable-mcp", "name": "bitable-mcp", "display_name": "Bitable", "description": "MCP server provides access to Lark Bitable through the Model Context Protocol. It allows users to interact with Bitable tables using predefined tools.", "repository": {"type": "git", "url": "https://github.com/lloydzhou/bitable-mcp"}, "homepage": "https://github.com/lloydzhou/bitable-mcp", "author": {"name": "lloydzhou"}, "license": "MIT", "categories": ["API", "Bitable"], "tags": ["Bitable", "Lark"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["bitable-mcp"], "env": {"PERSONAL_BASE_TOKEN": "${PERSONAL_BASE_TOKEN}", "APP_TOKEN": "${APP_TOKEN}"}}, "python": {"type": "python", "command": "python", "args": ["-m", "bitable_mcp"], "env": {"PERSONAL_BASE_TOKEN": "${PERSONAL_BASE_TOKEN}", "APP_TOKEN": "${APP_TOKEN}"}, "description": "Run with Python module (requires pip install)"}}, "examples": [{"title": "List Tables", "description": "Lists all tables available in Bitable.", "prompt": "list_table"}], "arguments": {"PERSONAL_BASE_TOKEN": {"description": "Personal base token required for authentication with the Bitable API.", "required": true, "example": "your_personal_base_token"}, "APP_TOKEN": {"description": "Application token required for the Bitable server to function properly.", "required": true, "example": "your_app_token"}}}, {"id": "openapi-anyapi", "name": "openapi-anyapi", "display_name": "Scalable OpenAPI Endpoint Discovery Tool", "description": "Interact with large [OpenAPI](https://www.openapis.org/) docs using built-in semantic search for endpoints. Allows for customizing the MCP server prefix.", "repository": {"type": "git", "url": "https://github.com/baryhuang/mcp-server-any-openapi"}, "homepage": "https://github.com/baryhuang/mcp-server-any-openapi", "author": {"name": "baryhuang"}, "license": "MIT", "categories": ["API", "OpenAPI", "Server"], "installations": {"docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "--rm", "-e", "OPENAPI_JSON_DOCS_URL=${OPENAPI_JSON_DOCS_URL}", "-e", "API_REQUEST_BASE_URL=${API_REQUEST_BASE_URL}", "-e", "MCP_API_PREFIX=${MCP_API_PREFIX}", "buryhuang/mcp-server-any-openapi:latest"], "env": {"OPENAPI_JSON_DOCS_URL": "${OPENAPI_JSON_DOCS_URL}", "API_REQUEST_BASE_URL": "${API_REQUEST_BASE_URL}", "MCP_API_PREFIX": "${MCP_API_PREFIX}"}}, "uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/baryhuang/mcp-server-any-openapi", "src/mcp_server_any_openapi/server.py"], "env": {"OPENAPI_JSON_DOCS_URL": "${OPENAPI_JSON_DOCS_URL}", "API_REQUEST_BASE_URL": "${API_REQUEST_BASE_URL}", "MCP_API_PREFIX": "${MCP_API_PREFIX}"}}}, "tags": ["OpenAPI", "API Discovery", "Semantic Search", "FastAPI"], "examples": [{"title": "Get API Endpoints", "description": "Use this tool to find relevant API endpoints by describing your intent.", "prompt": "Get prices for all stocks"}], "arguments": {"OPENAPI_JSON_DOCS_URL": {"description": "URL to the OpenAPI specification JSON (defaults to https://api.staging.readymojo.com/openapi.json)", "required": false, "example": "https://api.example.com/openapi.json"}, "API_REQUEST_BASE_URL": {"description": "Optional base URL to override the default URL extracted from the OpenAPI document.", "required": false, "example": "https://api.finance.com"}, "MCP_API_PREFIX": {"description": "Customizable tool namespace (default 'any_openapi'). Allows for control over tool naming.", "required": false, "example": "finance"}}}, {"id": "blender", "name": "blender", "display_name": "<PERSON><PERSON>der", "description": "Blender integration allowing prompt enabled 3D scene creation, modeling and manipulation.", "repository": {"type": "git", "url": "https://github.com/ahujasid/blender-mcp"}, "homepage": "https://github.com/ahujasid/blender-mcp", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["3D Modeling", "AI Integration"], "tags": ["<PERSON><PERSON>der", "<PERSON>", "3D Modeling"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["blender-mcp"]}}}, {"id": "virtual-location-google-street-view-etc", "name": "virtual-location-google-street-view-etc", "display_name": "Virtual Traveling Bot", "description": "Integrates Google Map, Google Street View, PixAI, Stability.ai, ComfyUI API and Bluesky to provide a virtual location simulation in LLM (written in Effect.ts)", "repository": {"type": "git", "url": "https://github.com/mfukushim/map-traveler-mcp"}, "homepage": "https://github.com/mfukushim/map-traveler-mcp", "author": {"name": "mfukus<PERSON>"}, "license": "MIT", "categories": ["Travel", "Virtual Reality"], "tags": ["Google Maps", "Avatar", "Virtual Travel"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@mfukushim/map-traveler-mcp"], "env": {"GoogleMapApi_key": "${GoogleMapApi_key}", "mapApi_url": "${mapApi_url}", "time_scale": "${time_scale}", "sqlite_path": "${sqlite_path}", "rembg_path": "${rembg_path}", "remBgUrl": "${remBgUrl}", "pixAi_key": "${pixAi_key}", "sd_key": "${sd_key}", "pixAi_modelId": "${pixAi_modelId}", "comfy_url": "${comfy_url}", "comfy_workflow_t2i": "${comfy_workflow_t2i}", "comfy_workflow_i2i": "${comfy_workflow_i2i}", "comfy_params": "${comfy_params}", "fixed_model_prompt": "${fixed_model_prompt}", "bodyAreaRatio": "${bodyAreaRatio}", "bodyHWRatio": "${bodyHWRatio}", "bodyWindowRatioW": "${bodyWindowRatioW}", "bodyWindowRatioH": "${bodyWindowRatioH}", "bs_id": "${bs_id}", "bs_pass": "${bs_pass}", "bs_handle": "${bs_handle}", "filter_tools": "${filter_tools}", "moveMode": "${moveMode}", "image_width": "${image_width}", "DATABASE_URL": "${DATABASE_URL}"}}}, "examples": [{"title": "Travel to Tokyo", "description": "Instruct the avatar to travel to Tokyo Station.", "prompt": "Go to Tokyo Station."}, {"title": "Current Location Info", "description": "Get the current location information of the avatar.", "prompt": "Where are you now?"}], "arguments": {"GoogleMapApi_key": {"description": "API key for accessing Google Maps services.", "required": true, "example": "YOUR_GOOGLE_MAP_API_KEY"}, "mapApi_url": {"description": "Custom endpoint for the Map API, if any; otherwise, the default endpoint is used.", "required": false, "example": "https://your-custom-map-api.com"}, "time_scale": {"description": "Scale factor to adjust the travel time based on real roads duration; default is 4.", "required": false, "example": "5"}, "sqlite_path": {"description": "Path for saving the SQLite database file. It determines where the travel log will be stored.", "required": true, "example": "%USERPROFILE%/Desktop/traveler.sqlite"}, "rembg_path": {"description": "Absolute path of the installed rembg command line interface for removing backgrounds from images.", "required": true, "example": "C:\\path\\to\\your\\rembg.exe"}, "remBgUrl": {"description": "URL for the rembg API service if used; this is an alternative to the command line interface.", "required": false, "example": "http://rembg:7000"}, "pixAi_key": {"description": "API key for accessing PixAI image generation services; either this or sd_key must be set to use image generation.", "required": true, "example": "YOUR_PIXAI_API_KEY"}, "sd_key": {"description": "API key for accessing Stability.ai image generation services; either this or pixAi_key must be set.", "required": true, "example": "YOUR_STABILITY_AI_API_KEY"}, "pixAi_modelId": {"description": "ID for the PixAI model to be used, if not set, the default model will be used.", "required": false, "example": "1648918127446573124"}, "comfy_url": {"description": "URL to the ComfyUI API for image generation; must be set if using ComfyUI for this purpose.", "required": false, "example": "http://*************:8188"}, "comfy_workflow_t2i": {"description": "Path to the workflow JSON file for text-to-image conversion in ComfyUI.", "required": false, "example": "C:\\path\\to\\workflow\\t2i.json"}, "comfy_workflow_i2i": {"description": "Path to the workflow JSON file for image-to-image conversion in ComfyUI.", "required": false, "example": "C:\\path\\to\\workflow\\i2i.json"}, "comfy_params": {"description": "Parameters for the ComfyUI workflow in key-value format, received during the request.", "required": false, "example": "key1=value1,key2=value2"}, "fixed_model_prompt": {"description": "A fixed prompt for avatar generation that prevents changes during conversations.", "required": false, "example": "Generate a friendly avatar."}, "bodyAreaRatio": {"description": "Acceptable ratio for the avatar image area; affects how much of the image is used for the avatar.", "required": false, "example": "0.042"}, "bodyHWRatio": {"description": "Acceptable aspect ratios for the avatar image; ensures correct proportions for the avatar.", "required": false, "example": "1.5~2.3"}, "bodyWindowRatioW": {"description": "Horizontal ratio for the avatar composite window; affects layout.", "required": false, "example": "0.5"}, "bodyWindowRatioH": {"description": "Aspect ratio for the avatar composite window; also affects layout.", "required": false, "example": "0.75"}, "bs_id": {"description": "<PERSON><PERSON> SNS registration address for posting travel updates.", "required": false, "example": "YOUR_BSKY_ID"}, "bs_pass": {"description": "<PERSON><PERSON> SNS password for the dedicated account used for posting.", "required": false, "example": "YOUR_BSKY_PASSWORD"}, "bs_handle": {"description": "Bluesky SNS handle name for the account; used in the posts.", "required": false, "example": "myusername.bsky.social"}, "filter_tools": {"description": "Settings to filter the tools available for use; all tools will be available by default.", "required": false, "example": "tips,set_traveler_location"}, "moveMode": {"description": "Indicates whether the movement mode is realtime or skip; default is realtime.", "required": false, "example": "realtime"}, "image_width": {"description": "Width of the generated output image in pixels; the default is 512.", "required": false, "example": "512"}, "DATABASE_URL": {"description": "Database URL for persistent storage; used if a different database should be connected.", "required": false, "example": "mysql://user:password@host/dbname"}}}, {"id": "multicluster-mcp-sever", "name": "multicluster-mcp-sever", "display_name": "Multi-Cluster Server", "description": "The gateway for GenAI systems to interact with multiple Kubernetes clusters.", "repository": {"type": "git", "url": "https://github.com/yanmxa/multicluster-mcp-server"}, "homepage": "https://github.com/yanmxa/multicluster-mcp-server", "author": {"name": "yanmxa"}, "license": "MIT", "categories": ["Kubernetes", "Multi-Cluster Management"], "tags": ["Generative AI", "Kubernetes"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/yanmxa/multicluster-mcp-server"]}}}, {"id": "google-maps", "name": "google-maps", "display_name": "Google Maps", "description": "Location services, directions, and place details", "repository": {"type": "git", "url": "https://github.com/modelcontextprotocol/servers"}, "homepage": "https://github.com/modelcontextprotocol/servers/blob/main/src/google-maps", "author": {"name": "modelcontextprotocol"}, "license": "MIT", "categories": ["API", "Mapping"], "tags": ["Google Maps", "Geolocation"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-google-maps"], "env": {"GOOGLE_MAPS_API_KEY": "${GOOGLE_MAPS_API_KEY}"}}, "docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "--rm", "-e", "GOOGLE_MAPS_API_KEY", "mcp/google-maps"], "env": {"GOOGLE_MAPS_API_KEY": "${GOOGLE_MAPS_API_KEY}"}}}, "arguments": {"GOOGLE_MAPS_API_KEY": {"description": "Your Google Maps API key obtained from the Google Developers Console.", "required": true, "example": "AIzaSyD..."}}}, {"id": "spotify", "name": "spotify", "display_name": "Spotify", "description": "This MCP allows an LLM to play and use Spotify.", "repository": {"type": "git", "url": "https://github.com/varunneal/spotify-mcp"}, "homepage": "https://github.com/varunneal/spotify-mcp", "author": {"name": "varunneal"}, "license": "MIT", "categories": ["multimedia", "music"], "tags": ["spotify", "audio"], "examples": [{"title": "Basic Playback Control", "description": "Use the MCP to start, pause, or skip songs on Spotify.", "prompt": "Start playing a song on Spotify."}, {"title": "Search for Tracks", "description": "Search for tracks, albums, artists, or playlists using the Spotify API.", "prompt": "Search for the album 'Thriller'."}], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/varunneal/spotify-mcp", "spotify-mcp"], "env": {"SPOTIFY_CLIENT_ID": "${SPOTIFY_CLIENT_ID}", "SPOTIFY_CLIENT_SECRET": "${SPOTIFY_CLIENT_SECRET}", "SPOTIFY_REDIRECT_URI": "${SPOTIFY_REDIRECT_URI}"}}}, "arguments": {"SPOTIFY_CLIENT_ID": {"description": "The client ID for your Spotify application, required to authenticate with the Spotify API.", "required": true, "example": "your_spotify_client_id_here"}, "SPOTIFY_CLIENT_SECRET": {"description": "The client secret for your Spotify application, needed for secure authentication with the API.", "required": true, "example": "your_spotify_client_secret_here"}, "SPOTIFY_REDIRECT_URI": {"description": "The redirect URI you specified when creating the Spotify application, needed for the OAuth authentication process.", "required": false, "example": "http://localhost:8888"}}}, {"id": "any-chat-completions", "name": "any-chat-completions", "display_name": "Any Chat Completions", "description": "Interact with any OpenAI SDK Compatible Chat Completions API like OpenAI, Perplexity, Groq, xAI and many more.", "repository": {"type": "git", "url": "https://github.com/pyroprompts/any-chat-completions-mcp"}, "homepage": "https://github.com/pyroprompts/any-chat-completions-mcp", "author": {"name": "pyroprompts"}, "license": "MIT", "categories": ["Cha<PERSON>", "Integration"], "tags": ["<PERSON>", "OpenAI", "API", "Chat Completion"], "examples": [{"title": "OpenAI Integration", "description": "Integrate OpenAI into Claude Desktop", "prompt": "Configure the MCP server to use OpenAI's API."}], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/pyroprompts/any-chat-completions-mcp"], "env": {"AI_CHAT_KEY": "${AI_CHAT_KEY}", "AI_CHAT_NAME": "${AI_CHAT_NAME}", "AI_CHAT_MODEL": "${AI_CHAT_MODEL}", "AI_CHAT_BASE_URL": "${AI_CHAT_BASE_URL}"}}}, "arguments": {"AI_CHAT_KEY": {"description": "API key for authentication with the chat service provider.", "required": true, "example": "your_openai_secret_key_here"}, "AI_CHAT_NAME": {"description": "The name of the AI chat provider to use, like 'OpenAI' or 'PyroPrompts'.", "required": true, "example": "OpenAI"}, "AI_CHAT_MODEL": {"description": "Specifies which model to be used for the chat service, e.g., 'gpt-4o'.", "required": true, "example": "gpt-4o"}, "AI_CHAT_BASE_URL": {"description": "The base URL for the API service of the chat provider.", "required": true, "example": "https://api.openai.com/v1"}}}, {"id": "google-tasks", "name": "google-tasks", "display_name": "Google Tasks", "description": "Google Tasks API Model Context Protocol Server.", "repository": {"type": "git", "url": "https://github.com/zcaceres/gtasks-mcp"}, "homepage": "https://github.com/zcaceres/gtasks-mcp", "author": {"name": "zcaceres"}, "license": "MIT", "categories": ["productivity", "task management"], "tags": ["google", "tasks", "productivity"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/zcaceres/gtasks-mcp"]}}}, {"id": "xmind", "name": "xmind", "display_name": "XMind", "description": "Read and search through your XMind directory containing XMind files.", "repository": {"type": "git", "url": "https://github.com/apeyroux/mcp-xmind"}, "homepage": "https://github.com/apeyroux/mcp-xmind", "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "categories": ["Productivity", "Analysis"], "tags": ["XMind", "Mind Mapping", "Analysis", "Productivity"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@41px/mcp-xmind", "${USER_XMIND_DIRECTORY}"]}}, "examples": [{"title": "Search for Nodes", "description": "Searches through the mind map for specific nodes based on the query parameters.", "prompt": "{\"name\": \"search_nodes\", \"arguments\": {\"path\": \"/path/to/file.xmind\", \"query\": \"project\", \"searchIn\": [\"title\", \"notes\"], \"caseSensitive\": false}}"}, {"title": "Extract Node", "description": "Extracts a node from the mind map based on a search query.", "prompt": "{\"name\": \"extract_node\", \"arguments\": {\"path\": \"/path/to/file.xmind\", \"searchQuery\": \"Feature > API\"}}"}, {"title": "List Tasks", "description": "Lists TODO tasks from the mind map.", "prompt": "{\"name\": \"get_todo_tasks\", \"arguments\": {\"path\": \"/path/to/file.xmind\"}}"}], "arguments": {"USER_XMIND_DIRECTORY": {"description": "The path to the directory containing XMind files that should be processed by the server.", "required": true, "example": "/Users/<USER>/XMind"}}}, {"id": "influxdb", "name": "influxdb", "display_name": "InfluxDB", "description": "Run queries against InfluxDB OSS API v2.", "repository": {"type": "git", "url": "https://github.com/idoru/influxdb-mcp-server"}, "homepage": "https://github.com/idoru/influxdb-mcp-server", "author": {"name": "idoru"}, "license": "MIT", "categories": ["database"], "tags": ["InfluxDB", "API", "server", "time-series"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["${INFLUXDB_TOKEN}", "${INFLUXDB_URL}", "${INFLUXDB_ORG}"], "env": {"INFLUXDB_TOKEN": "your_token", "INFLUXDB_URL": "http://localhost:8086", "INFLUXDB_ORG": "your_org"}}}, "arguments": {"INFLUXDB_TOKEN": {"description": "Authentication token for the InfluxDB API", "required": true, "example": "your_token"}, "INFLUXDB_URL": {"description": "URL of the InfluxDB instance", "required": false, "example": "http://localhost:8086"}, "INFLUXDB_ORG": {"description": "Default organization name for certain operations", "required": false, "example": "your_org"}}}, {"id": "mssql", "name": "mssql", "display_name": "MSSQL", "description": "MCP Server for MSSQL database in Python", "repository": {"type": "git", "url": "https://github.com/JexinSam/mssql_mcp_server"}, "homepage": "https://github.com/JexinSam/mssql_mcp_server", "author": {"name": "JexinSam"}, "license": "MIT", "categories": ["Database", "MCP"], "tags": ["MSSQL", "AI", "Database Access"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["mssql_mcp_server"], "env": {"MSSQL_DRIVER": "${MSSQL_DRIVER}", "MSSQL_HOST": "${MSSQL_HOST}", "MSSQL_USER": "${MSSQL_USER}", "MSSQL_PASSWORD": "${MSSQL_PASSWORD}", "MSSQL_DATABASE": "${MSSQL_DATABASE}"}}}, "arguments": {"MSSQL_DRIVER": {"description": "Environment variable that specifies the driver to connect to the MSSQL database.", "required": true, "example": "mssql_driver"}, "MSSQL_HOST": {"description": "Environment variable that specifies the hostname or IP address of the MSSQL server.", "required": true, "example": "localhost"}, "MSSQL_USER": {"description": "Environment variable that defines the username for connecting to the MSSQL database.", "required": true, "example": "your_username"}, "MSSQL_PASSWORD": {"description": "Environment variable that stores the password for the MSSQL user.", "required": true, "example": "your_password"}, "MSSQL_DATABASE": {"description": "Environment variable that specifies the name of the MSSQL database to connect to.", "required": true, "example": "your_database"}}}, {"id": "n8n", "name": "n8n", "display_name": "n8n", "description": "This MCP server provides tools and resources for AI assistants to manage n8n workflows and executions, including listing, creating, updating, and deleting workflows, as well as monitoring their execution status.", "repository": {"type": "git", "url": "https://github.com/leonardsellem/n8n-mcp-server"}, "homepage": "https://github.com/leonardsellem/n8n-mcp-server", "author": {"name": "le<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["Workflow", "AI", "Automation"], "tags": ["n8n", "server", "AI"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@anaisbetts/mcp-installer"], "env": {"N8N_API_URL": "${N8N_API_URL}", "N8N_API_KEY": "${N8N_API_KEY}"}}}, "arguments": {"N8N_API_URL": {"description": "URL of the n8n API", "required": true, "example": "http://localhost:5678/api/v1"}, "N8N_API_KEY": {"description": "API key for authenticating with n8n", "required": true, "example": "n8n_api_..."}}}, {"id": "bing-web-search-api", "name": "bing-web-search-api", "display_name": "Bing Search API", "description": "Server implementation for Microsoft Bing Web Search API.", "repository": {"type": "git", "url": "https://github.com/leehanchung/bing-search-mcp"}, "homepage": "https://github.com/leehanchung/bing-search-mcp", "author": {"name": "leehanchung"}, "license": "MIT", "categories": ["API", "Search"], "tags": ["<PERSON>", "Search", "Web", "News", "Images"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "http://github.com/leehanchung/bing-search-mcp", "mcp_server_bin_search"], "env": {"BING_API_KEY": "${BING_API_KEY}"}}}, "examples": [{"title": "Web Search Example", "description": "Search the web for various queries.", "prompt": "Search for 'latest technology news'."}, {"title": "News Search Example", "description": "Search for the latest news articles.", "prompt": "Search for 'global warming'."}, {"title": "Image Search Example", "description": "Find images related to a query.", "prompt": "Search for 'sunsets'."}], "arguments": {"BING_API_KEY": {"description": "API key required for authenticating requests to the Microsoft Bing Search API.", "required": true, "example": "your-bing-api-key"}}}, {"id": "image-generation", "name": "image-generation", "display_name": "Image Generation", "description": "This MCP server provides image generation capabilities using the Replicate Flux model.", "repository": {"type": "git", "url": "https://github.com/GongRzhe/Image-Generation-MCP-Server"}, "homepage": "https://github.com/GongRzhe/Image-Generation-MCP-Server", "author": {"name": "GongRzhe"}, "license": "MIT", "categories": ["image generation"], "tags": ["image", "generation", "flux", "Replicate"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["@gongrzhe/image-gen-server"], "env": {"REPLICATE_API_TOKEN": "${REPLICATE_API_TOKEN}", "MODEL": "${MODEL}", "your-replicate-api-token": "${your_replicate_api_token}", "alternative-model-name": "${alternative_model_name}"}}}, "arguments": {"REPLICATE_API_TOKEN": {"description": "Your Replicate API token for authentication", "required": true, "example": "your-replicate-api-token"}, "MODEL": {"description": "The Replicate model to use for image generation. Defaults to \"black-forest-labs/flux-schnell\"", "required": false, "example": "alternative-model-name"}}}, {"id": "aws-s3", "name": "aws-s3", "display_name": "Sample S3 Model Context Protocol", "description": "A sample MCP server for AWS S3 that flexibly fetches objects from S3 such as PDF documents.", "repository": {"type": "git", "url": "https://github.com/aws-samples/sample-mcp-server-s3"}, "homepage": "https://github.com/aws-samples/sample-mcp-server-s3", "author": {"name": "aws-samples"}, "license": "MIT", "categories": ["cloud", "data", "aws"], "tags": ["S3", "PDF", "aws"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["s3-mcp-server"]}}}, {"id": "markdownify", "name": "markdownify", "display_name": "Markdownify", "description": "MCP to convert almost anything to Markdown (PPTX, HTML, PDF, Youtube Transcripts and more)", "repository": {"type": "git", "url": "https://github.com/zcaceres/mcp-markdownify-server"}, "homepage": "https://github.com/zcaceres/mcp-markdownify-server", "license": "MIT", "categories": ["Conversion", "<PERSON><PERSON>"], "tags": ["markdown", "conversion"], "author": {"name": "zcaceres"}, "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/zcaceres/mcp-markdownify-server"], "env": {"UV_PATH": "${UV_PATH}"}}}, "arguments": {"UV_PATH": {"description": "Environment variable specifying the installation location of the `uv` dependency.", "required": false, "example": "/path/to/uv"}}}, {"id": "openapi-schema", "name": "openapi-schema", "display_name": "OpenAPI Schema Model Context Protocol", "description": "Allow LLMs to explore large [OpenAPI](https://www.openapis.org/) schemas without bloating the context.", "repository": {"type": "git", "url": "https://github.com/hannesj/mcp-openapi-schema"}, "homepage": "https://github.com/hannesj/mcp-openapi-schema", "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "[NOT GIVEN]", "categories": ["API", "Development"], "tags": ["OpenAPI", "LLM"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "mcp-openapi-schema", "/ABSOLUTE/PATH/TO/openapi.yaml"]}}, "arguments": {"ABSOLUTE_PATH_TO_OPENAPI_YAML": {"description": "The absolute path to the OpenAPI YAML file that the MCP server will use to load the schema.", "required": true, "example": "/absolute/path/to/openapi.yaml"}}}, {"id": "xcodebuild", "name": "xcodebuild", "display_name": "Xcode Build", "description": "🍎 Build iOS Xcode workspace/project and feed back errors to llm.", "repository": {"type": "git", "url": "https://github.com/ShenghaiWang/xcodebuild"}, "homepage": "https://github.com/ShenghaiWang/xcodebuild", "author": {"name": "ShenghaiWang"}, "license": "MIT", "categories": ["iOS", "Development"], "tags": ["xcode", "mcpxcodebuild"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["mcpxcodebuild"]}, "python": {"type": "python", "command": "python", "args": ["-m", "mcpxcodebuild"]}}, "examples": [{"title": "Build iOS Project", "description": "Builds the iOS Xcode workspace/project located at a specified folder.", "prompt": "build --folder /path/to/your/project"}]}, {"id": "azure-adx", "name": "azure-adx", "display_name": "Azure Data Explorer", "description": "Query and analyze Azure Data Explorer databases.", "repository": {"type": "git", "url": "https://github.com/pab1it0/adx-mcp-server"}, "homepage": "https://github.com/pab1it0/adx-mcp-server", "author": {"name": "pab1it0"}, "license": "MIT", "categories": ["data", "query"], "tags": ["Azure", "KQL", "Data Explorer"], "installations": {"docker": {"type": "docker", "command": "docker", "args": ["run", "--rm", "-i", "-e", "ADX_CLUSTER_URL", "-e", "ADX_DATABASE", "adx-mcp-server"], "env": {"ADX_CLUSTER_URL": "${ADX_CLUSTER_URL}", "ADX_DATABASE": "${ADX_DATABASE}"}}}, "arguments": {"ADX_CLUSTER_URL": {"description": "The URL of the Azure Data Explorer cluster.", "required": true, "example": "https://yourcluster.region.kusto.windows.net"}, "ADX_DATABASE": {"description": "The name of the Azure Data Explorer database to connect to.", "required": true, "example": "your_database"}}}, {"id": "llm-context", "name": "llm-context", "display_name": "LLM Context", "description": "Provides a repo-packing MCP tool with configurable profiles that specify file inclusion/exclusion patterns and optional prompts.", "repository": {"type": "git", "url": "https://github.com/cyberchitta/llm-context.py"}, "homepage": "https://github.com/cyberchitta/llm-context.py", "author": {"name": "cyberchitta"}, "license": "Apache 2.0", "categories": ["Development Tools", "AI", "Chat Interfaces"], "tags": ["LLM", "Context Injection", "Development", "ChatGPT", "Productivity"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "llm-context", "lc-mcp"]}}, "arguments": {"mcp": {"description": "Indicates the model context protocol that should be used for communication.", "required": true, "example": "lc-mcp"}}}, {"id": "gmail-headless", "name": "gmail-headless", "display_name": "Headless Gmail Server", "description": "Remote hostable MCP server that can get and send Gmail messages without local credential or file system setup.", "repository": {"type": "git", "url": "https://github.com/baryhuang/mcp-headless-gmail"}, "homepage": "https://github.com/baryhuang/mcp-headless-gmail", "author": {"name": "baryhuang"}, "license": "MIT", "categories": ["Email", "Gmail"], "tags": ["Gmail", "Headless", "<PERSON>er", "API"], "installations": {"docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "--rm", "buryhuang/mcp-headless-gmail:latest"]}}, "arguments": {"google_access_token": {"description": "OAuth 2.0 access token for authenticating API requests to Google services.", "required": true, "example": "your_access_token"}, "google_refresh_token": {"description": "OAuth 2.0 refresh token used to obtain a new access token when the original access token expires.", "required": true, "example": "your_refresh_token"}, "google_client_id": {"description": "Client ID obtained from Google Cloud Console, used to identify the application.", "required": true, "example": "your_client_id"}, "google_client_secret": {"description": "Client secret obtained from Google Cloud Console, used alongside the client ID for authentication.", "required": true, "example": "your_client_secret"}}}, {"id": "mac-messages-mcp", "name": "mac-messages-mcp", "display_name": "Mac Messages", "description": "An MCP server that securely interfaces with your iMessage database via the Model Context Protocol (MCP), allowing LLMs to query and analyze iMessage conversations. It includes robust phone number validation, attachment processing, contact management, group chat handling, and full support for sending and receiving messages.", "repository": {"type": "git", "url": "https://github.com/carterlasalle/mac_messages_mcp"}, "homepage": "https://github.com/carterlasalle/mac_messages_mcp", "author": {"name": "<PERSON><PERSON><PERSON>alle"}, "license": "MIT", "categories": ["communication", "utility"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["mac-messages-mcp"]}}, "tags": ["python", "mac", "messages"]}, {"id": "llamacloud", "name": "llamacloud", "display_name": "LlamaCloud", "description": "Integrate the data stored in a managed index on [LlamaCloud](https://cloud.llamaindex.ai/)", "repository": {"type": "git", "url": "https://github.com/run-llama/mcp-server-llamacloud"}, "homepage": "https://github.com/run-llama/mcp-server-llamacloud", "author": {"name": "run-llama"}, "license": "MIT", "categories": ["Tools"], "tags": ["LlamaCloud", "TypeScript"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@llamaindex/mcp-server-llamacloud"], "env": {"LLAMA_CLOUD_PROJECT_NAME": "${LLAMA_CLOUD_PROJECT_NAME}", "LLAMA_CLOUD_API_KEY": "${LLAMA_CLOUD_API_KEY}"}}}, "arguments": {"LLAMA_CLOUD_PROJECT_NAME": {"description": "The name of your LlamaCloud project that you want to use with the transfer tools.", "required": true, "example": "MyProject"}, "LLAMA_CLOUD_API_KEY": {"description": "Your API key for accessing LlamaCloud services, which is necessary for authentication.", "required": true, "example": "**********abcdef"}}}, {"id": "replicate", "name": "replicate", "display_name": "Replicate", "description": "Search, run and manage machine learning models on Replicate through a simple tool-based interface. Browse models, create predictions, track their status, and handle generated images.", "repository": {"type": "git", "url": "https://github.com/deepfates/mcp-replicate"}, "homepage": "https://github.com/deepfates/mcp-replicate", "author": {"name": "deepfates"}, "license": "MIT", "categories": ["AI"], "tags": ["Replicate", "API"], "examples": [{"title": "Run a model prediction", "description": "Creates a prediction using a specified model and input parameters.", "prompt": "create_prediction(model_id='model_id_here', input_params='input_params_here')"}], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["mcp-replicate"], "env": {"REPLICATE_API_TOKEN": "${REPLICATE_API_TOKEN}"}}}, "arguments": {"REPLICATE_API_TOKEN": {"description": "Your Replicate API token to authenticate requests to the Replicate API. Needed for the server to function and fetch models or execute predictions.", "required": true, "example": "your_token_here"}}}, {"id": "brave-search", "name": "brave-search", "display_name": "Brave Search", "description": "Web and local search using Brave's Search API", "repository": {"type": "git", "url": "https://github.com/modelcontextprotocol/servers"}, "homepage": "https://github.com/modelcontextprotocol/servers/blob/main/src/brave-search", "author": {"name": "modelcontextprotocol"}, "license": "MIT", "categories": ["search", "api"], "tags": ["brave", "search", "web", "local"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "${BRAVE_API_KEY}"}}, "docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "--rm", "-e", "BRAVE_API_KEY", "mcp/brave-search"], "env": {"BRAVE_API_KEY": "${BRAVE_API_KEY}"}}}, "examples": [{"title": "Web Search Example", "description": "Execute a web search with pagination and filtering.", "prompt": "brave_web_search(query=\"example search\", count=10, offset=0)"}, {"title": "Local Search Example", "description": "Search for local businesses and services.", "prompt": "brave_local_search(query=\"restaurants near me\", count=5)"}], "arguments": {"BRAVE_API_KEY": {"description": "The API key required to authenticate requests to the Brave Search API.", "required": true, "example": "YOUR_API_KEY_HERE"}}}, {"id": "naver", "name": "naver", "display_name": "Naver", "description": "This MCP server provides tools to interact with various Naver services, such as searching blogs, news, books, and more.", "repository": {"type": "git", "url": "https://github.com/pfldy2850/py-mcp-naver"}, "homepage": "https://github.com/pfldy2850/py-mcp-naver", "author": {"name": "pfldy2850"}, "license": "MIT", "categories": ["API", "Naver", "OpenAPI"], "tags": ["Naver", "API", "OpenAPI", "Search"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/pfldy2850/py-mcp-naver.git", "src/server.py"], "env": {"NAVER_CLIENT_ID": "${NAVER_CLIENT_ID}", "NAVER_CLIENT_SECRET": "${NAVER_CLIENT_SECRET}"}}}, "examples": [{"title": "Search Blog Posts", "description": "Search blog posts on Naver using a query.", "prompt": "search_blog('your query here')"}, {"title": "Search News Articles", "description": "Search news articles on Naver using a query.", "prompt": "search_news('your query here')"}, {"title": "Search Books", "description": "Search books on Naver using a query.", "prompt": "search_book('your query here')"}], "arguments": {"NAVER_CLIENT_ID": {"description": "The Client ID for accessing the Naver Open API, obtained from the Naver developer portal.", "required": true, "example": "your_naver_client_id"}, "NAVER_CLIENT_SECRET": {"description": "The Client Secret for accessing the Naver Open API, obtained from the Naver developer portal.", "required": true, "example": "your_naver_client_secret"}}}, {"id": "kibela", "name": "kibela", "display_name": "<PERSON><PERSON><PERSON>", "description": "Interact with Kibela API.", "repository": {"type": "git", "url": "https://github.com/kiwamizamurai/mcp-kibela-server"}, "homepage": "https://github.com/kiwamizamurai/mcp-kibela-server", "author": {"name": "kiwamizamurai"}, "license": "MIT", "categories": ["Productivity"], "tags": ["<PERSON><PERSON><PERSON>", "Integration"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@kiwamizamurai/mcp-kibela-server"], "env": {"KIBELA_TEAM": "${KIBELA_TEAM}", "KIBELA_TOKEN": "${KIBELA_TOKEN}"}}}, "examples": [{"title": "Search Kibela notes", "description": "Search through your Kibela notes using a query.", "prompt": "kibela_search_notes(\"my search query\")"}, {"title": "Get latest notes", "description": "Retrieve your latest notes from <PERSON><PERSON><PERSON>.", "prompt": "kibela_get_my_notes()"}, {"title": "Get note content", "description": "Fetch content of a specific note by ID.", "prompt": "kibela_get_note_content(\"note-id\")"}], "arguments": {"KIBELA_TEAM": {"description": "Your Kibela team name", "required": true, "example": "your-team"}, "KIBELA_TOKEN": {"description": "Your Kibela API token", "required": true, "example": "your-token"}}}, {"id": "whale-tracker-mcp", "name": "whale-tracker-mcp", "display_name": "<PERSON><PERSON> Tracker", "description": "A mcp server for tracking cryptocurrency whale transactions.", "repository": {"type": "git", "url": "https://github.com/kukapay/whale-tracker-mcp"}, "homepage": "https://github.com/kukapay/whale-tracker-mcp", "author": {"name": "kukapay"}, "license": "MIT", "categories": ["cryptocurrency", "tracking", "API"], "tags": ["whale tracker", "cryptocurrency", "API"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/kukapay/whale-tracker-mcp", "whale-tracker-mcp"], "env": {"WHALE_TRACKER_API_KEY": "your_api_key_here"}}}, "examples": [{"title": "Fetch Recent Transactions", "description": "What are the latest whale transactions on Ethereum with a minimum value of $1,000,000?", "prompt": "What are the latest whale transactions on Ethereum with a minimum value of $1,000,000?"}, {"title": "Get Transaction Details", "description": "Tell me about transaction ID 123456789.", "prompt": "Tell me about transaction ID 123456789."}, {"title": "Analyze Whale Activity", "description": "Analyze recent whale transactions on Bitcoin.", "prompt": "Analyze recent whale transactions on Bitcoin."}], "arguments": {"WHALE_TRACKER_API_KEY": {"description": "Environment variable to load the Whale Alert API key for the server.", "required": true, "example": "your_api_key_here"}}}, {"id": "flightradar24", "name": "flightradar24", "display_name": "Flightradar24", "description": "A Claude Desktop MCP server that helps you track flights in real-time using Flightradar24 data.", "repository": {"type": "git", "url": "https://github.com/sunsetcoder/flightradar24-mcp-server"}, "author": {"name": "sunsetcoder"}, "license": "MIT", "examples": [{"title": "Check Flight Status", "description": "Ask for the status of a specific flight.", "prompt": "What's the status of flight UA123?"}, {"title": "Show Current Flights at Airport", "description": "Request to see all flights currently at an airport.", "prompt": "Show me all flights currently at SFO"}, {"title": "Emergency Flights Query", "description": "Ask if there are emergency flights in the area.", "prompt": "Are there any emergency flights in the area?"}, {"title": "International Flights Arrival", "description": "Request information on international flights arriving within a timeframe.", "prompt": "Show me all international flights arriving at SFO in the next 2 hours"}], "categories": ["Aviation", "Travel"], "tags": ["Flightradar24", "Flight Tracking"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/sunsetcoder/flightradar24-mcp-server"], "env": {"FR24_API_KEY": "${FR24_API_KEY}", "FR24_API_URL": "${FR24_API_URL}"}}}, "arguments": {"FR24_API_KEY": {"description": "Flightradar24 API key required for accessing flight data from the Flightradar24 API.", "required": true, "example": "your_actual_api_key_here"}, "FR24_API_URL": {"description": "The base URL for calling the Flightradar24 API for fetching real-time flight data.", "required": false, "example": "https://fr24api.flightradar24.com"}}}, {"id": "fantasy-pl", "name": "fantasy-pl", "display_name": "Fantasy Premier League", "description": "Give your coding agent direct access to up-to date Fantasy Premier League data", "repository": {"type": "git", "url": "https://github.com/rishijatia/fantasy-pl-mcp"}, "homepage": "https://github.com/rishijatia/fantasy-pl-mcp", "author": {"name": "rishijatia"}, "license": "MIT", "categories": ["sports", "data"], "tags": ["FPL", "fantasy", "football"], "installations": {"python": {"type": "python", "command": "python", "args": ["-m", "fpl_mcp"]}}, "examples": [{"title": "Compare Players", "description": "This example shows how to compare the statistics of two players.", "prompt": "Comp<PERSON> and <PERSON><PERSON><PERSON> over the last 5 gameweeks."}, {"title": "Find Players", "description": "This example demonstrates how to find players of a specific team.", "prompt": "Find all Arsenal midfielders."}, {"title": "Current Gameweek Status", "description": "This example prompts for the current gameweek status.", "prompt": "What's the current gameweek status?"}, {"title": "Top Forwards", "description": "This example retrieves the top 5 forwards by points.", "prompt": "Show me the top 5 forwards by points."}]}, {"id": "claude<PERSON>", "name": "claude<PERSON>", "display_name": "Claude Post Email Management", "description": "ClaudePost enables seamless email management for Gmail, offering secure features like email search, reading, and sending.", "repository": {"type": "git", "url": "https://github.com/ZilongXue/claude-post"}, "homepage": "https://github.com/ZilongXue/claude-post", "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["Email", "Productivity"], "tags": ["Email Management", "Natural Language Processing"], "examples": [{"title": "Search Emails", "description": "Search for emails using natural language commands.", "prompt": "Show me emails from last week."}, {"title": "Read Email Content", "description": "Request to read specific email content.", "prompt": "Show me the content of email #12345."}, {"title": "Send Emails", "description": "Send emails using voice commands.", "prompt": "I want to send an <NAME_EMAIL>."}], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/ZilongXue/claude-post", "src/email_client"]}}}, {"id": "quickchart", "name": "quickchart", "display_name": "Quickchart", "description": "A Model Context Protocol server for generating charts using QuickChart.io", "repository": {"type": "git", "url": "https://github.com/GongRzhe/Quickchart-MCP-Server"}, "homepage": "https://github.com/GongRzhe/Quickchart-MCP-Server", "author": {"name": "GongRzhe"}, "license": "MIT", "categories": ["charts", "development"], "tags": ["quickchart", "chart generation", "data visualization"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@gongrzhe/quickchart-mcp-server"]}}, "examples": [{"title": "Basic bar chart", "description": "Generate a bar chart using Chart.js configuration.", "prompt": "{\"type\":\"bar\",\"data\":{\"labels\":[\"January\",\"February\",\"March\"],\"datasets\":[{\"label\":\"Sales\",\"data\":[65,59,80],\"backgroundColor\":\"rgb(75,192,192)\"}]},\"options\":{\"title\":{\"display\":true,\"text\":\"Monthly Sales\"}}}"}], "arguments": {"client": {"description": "Specifies the client type for which the QuickChart Server is installed. In this case, it's for <PERSON>.", "required": true, "example": "claude"}}}, {"id": "puppeteer", "name": "puppeteer", "display_name": "Puppeteer Browser Automation", "description": "Browser automation and web scraping", "repository": {"type": "git", "url": "https://github.com/modelcontextprotocol/servers"}, "homepage": "https://github.com/modelcontextprotocol/servers/blob/main/src/puppeteer", "author": {"name": "modelcontextprotocol"}, "license": "MIT", "categories": ["browser automation", "web interaction"], "tags": ["puppeteer", "automation", "javascript", "screenshots", "web"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}, "docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "--rm", "--init", "-e", "DOCKER_CONTAINER=true", "mcp/puppeteer"]}}, "arguments": {"DOCKER_CONTAINER": {"description": "Environment variable to indicate that the application is running in Docker container mode", "required": false, "example": "true"}}}, {"id": "sqlite", "name": "sqlite", "display_name": "SQLite", "description": "Database interaction and business intelligence capabilities", "repository": {"type": "git", "url": "https://github.com/modelcontextprotocol/servers"}, "homepage": "https://github.com/modelcontextprotocol/servers/blob/main/src/sqlite", "author": {"name": "modelcontextprotocol"}, "license": "MIT", "categories": ["database", "business intelligence"], "tags": ["sqlite", "database", "business insights"], "installations": {"docker": {"type": "docker", "command": "docker", "args": ["run", "--rm", "-i", "-v", "mcp-test:/mcp", "mcp/sqlite", "--db-path", "/mcp/test.db"]}}, "examples": [{"title": "Interactive SQL Analysis", "description": "Guides users through database operations and insights generation.", "prompt": "mcp-demo -topic [business_domain]"}]}, {"id": "dbhub", "name": "dbhub", "display_name": "DBHub - Universal Database Gateway", "description": "Universal database MCP server connecting to MySQL, PostgreSQL, SQLite, DuckDB and etc.", "repository": {"type": "git", "url": "https://github.com/bytebase/dbhub"}, "homepage": "https://github.com/bytebase/dbhub/", "author": {"name": "bytebase"}, "license": "MIT", "categories": ["Database", "Gateway"], "tags": ["Database Gateway", "PostgreSQL", "MySQL", "SQL Server", "SQLite"], "installations": {"docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "--rm", "bytebase/dbhub", "--transport", "stdio", "--dsn", "${DATABASE_URL}"]}, "npx": {"type": "npm", "command": "npx", "args": ["-y", "@bytebase/dbhub", "--transport", "stdio", "--dsn", "${DATABASE_URL}"]}}, "arguments": {"DATABASE_URL": {"description": "The database connection string which includes the user, password, host, port, and database name.", "required": true, "example": "postgres://user:password@localhost:5432/dbname?sslmode=disable"}}}, {"id": "obsidian-mcp", "name": "obsidian-mcp", "display_name": "Obsidian", "description": "(by <PERSON>) An MCP server for Obsidian.md with tools for searching, reading, writing, and organizing notes.", "repository": {"type": "git", "url": "https://github.com/StevenStavrakis/obsidian-mcp"}, "homepage": "https://github.com/StevenStavrakis/obsidian-mcp", "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["Productivity", "Notes"], "tags": ["Obsidian", "AI", "Notes", "Productivity"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "obsidian-mcp", "/path/to/your/vault", "/path/to/your/vault2"]}}, "examples": [{"title": "Read a note", "description": "Read the contents of a note.", "prompt": "read-note('note-id')"}, {"title": "Create a new note", "description": "Create a new note in the vault.", "prompt": "create-note('note-name', 'note-content')"}]}, {"id": "scholarly", "name": "scholarly", "display_name": "scholarly", "description": "A MCP server to search for scholarly and academic articles.", "repository": {"type": "git", "url": "https://github.com/adityak74/mcp-scholarly"}, "homepage": "https://github.com/adityak74/mcp-scholarly", "author": {"name": "adityak74"}, "license": "MIT", "categories": ["Scholarly", "Academic"], "tags": ["scholarly", "academic"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["mcp-scholarly"]}, "docker": {"type": "docker", "command": "docker", "args": ["run", "--rm", "-i", "mcp/scholarly"]}}, "arguments": {"keyword": {"description": "The keyword to search for articles in arXiv.", "required": true, "example": "machine learning"}}}, {"id": "fingertip", "name": "fingertip", "display_name": "<PERSON><PERSON><PERSON>", "description": "MCP server for Fingertip.com to search and create new sites.", "repository": {"type": "git", "url": "https://github.com/fingertip-com/fingertip-mcp"}, "homepage": "https://github.com/fingertip-com/fingertip-mcp", "author": {"name": "fingertip-com"}, "license": "MIT", "categories": ["API", "AI"], "tags": ["<PERSON><PERSON><PERSON>", "AI Assistants"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@fingertip/mcp"]}}}, {"id": "anki", "name": "anki", "display_name": "<PERSON><PERSON>", "description": "An MCP server for interacting with your [Anki](https://apps.ankiweb.net/) decks and cards.", "repository": {"type": "git", "url": "https://github.com/scorzeth/anki-mcp-server"}, "homepage": "https://github.com/scorzeth/anki-mcp-server", "author": {"name": "scorzeth"}, "license": "MIT", "categories": ["Productivity", "Education"], "tags": ["<PERSON><PERSON>", "Cards", "Review"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/scorzeth/anki-mcp-server"], "description": "Run with npx (requires npm install)"}}}, {"id": "obsidian-markdown-notes", "name": "obsidian-markdown-notes", "display_name": "Obsidian Markdown Notes", "description": "Read and search through your Obsidian vault or any directory containing Markdown notes", "repository": {"type": "git", "url": "https://github.com/calclavia/mcp-obsidian"}, "homepage": "https://github.com/calclavia/mcp-obsidian", "author": {"name": "calclavia"}, "license": "APGL-3.0", "categories": ["Connector", "<PERSON><PERSON>"], "tags": ["obsidian"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/smithery-ai/mcp-obsidian.git"]}}}, {"id": "video-editor", "name": "video-editor", "display_name": "Video Editor", "description": "A Model Context Protocol Server to add, edit, and search videos with [Video Jungle](https://www.video-jungle.com/).", "repository": {"type": "git", "url": "https://github.com/burningion/video-editing-mcp"}, "homepage": "https://github.com/burningion/video-editing-mcp", "author": {"name": "burningion"}, "license": "MIT", "categories": ["Video Editing"], "tags": ["video", "editing", "API"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/burningion/video-editing-mcp", "video-editor-mcp", "${YOURAPIKEY}"]}}, "examples": [{"title": "Add Video Example", "description": "Shows how to add a video from a URL.", "prompt": "can you download the video at https://www.youtube.com/shorts/RumgYaH5XYw and name it fly traps?"}, {"title": "Search Videos Example", "description": "Example of searching videos with a keyword.", "prompt": "can you search my videos for fly traps?"}, {"title": "Generate Edit Example", "description": "Creates an edit from found video segments.", "prompt": "can you create an edit of all the times the video says \"fly trap\"?"}], "arguments": {"YOURAPIKEY": {"description": "API key required to authenticate and communicate with Video Jungle services.", "required": true, "example": "YOURAPIKEY"}}}, {"id": "mongodb", "name": "mongodb", "display_name": "MongoDB", "description": "A Model Context Protocol Server for MongoDB.", "repository": {"type": "git", "url": "https://github.com/kiliczsh/mcp-mongo-server"}, "homepage": "https://github.com/kiliczsh/mcp-mongo-server", "author": {"name": "kili<PERSON><PERSON>"}, "license": "MIT", "categories": ["Database"], "tags": ["MongoDB", "LLM"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "mcp-mongo-server", "mongodb://muhammed:<EMAIL>/sample_namespace"]}}}, {"id": "data-exploration", "name": "data-exploration", "display_name": "Data Exploration", "description": "MCP server for autonomous data exploration on .csv-based datasets, providing intelligent insights with minimal effort. NOTE: Will execute arbitrary Python code on your machine, please use with caution!", "repository": {"type": "git", "url": "https://github.com/reading-plus-ai/mcp-server-data-exploration"}, "homepage": "https://github.com/reading-plus-ai/mcp-server-data-exploration", "author": {"name": "reading-plus-ai"}, "license": "MIT", "categories": ["Data Science", "Exploration"], "tags": ["data", "exploration"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["mcp-server-ds"]}}, "examples": [{"title": "California Real Estate Listing Prices", "description": "Exploring housing price trends in California using a dataset.", "prompt": "csv_path: Local path to the CSV file, topic: Housing price trends in California."}, {"title": "Weather in London", "description": "Investigating daily weather history in London using a dataset.", "prompt": "csv_path: Local path to the CSV file, topic: Weather in London."}]}, {"id": "tmdb", "name": "tmdb", "display_name": "TMDB", "description": "This MCP server integrates with The Movie Database (TMDB) API to provide movie information, search capabilities, and recommendations.", "repository": {"type": "git", "url": "https://github.com/Laksh-star/mcp-server-tmdb"}, "homepage": "https://github.com/Laksh-star/mcp-server-tmdb", "author": {"name": "<PERSON>ksh-star"}, "license": "MIT", "categories": ["movies", "api", "tmdb"], "tags": ["tmdb", "movies", "recommendations"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/Laksh-star/mcp-server-tmdb"], "env": {"TMDB_API_KEY": "${TMDB_API_KEY}"}}}, "examples": [{"title": "Search for Movies", "description": "Search for movies by title or keywords", "prompt": "\"Search for movies about artificial intelligence\""}, {"title": "Get Trending Movies", "description": "Get today's or this week's trending movies", "prompt": "\"What are the trending movies today?\""}, {"title": "Get Movie Recommendations", "description": "Get movie recommendations based on a movie ID", "prompt": "\"Get movie recommendations based on movie ID 550\""}, {"title": "Get Movie Details", "description": "Get details of a specific movie by ID", "prompt": "\"Tell me about the movie with ID 550\""}], "arguments": {"TMDB_API_KEY": {"description": "API key used to authenticate requests to the TMDB API.", "required": true, "example": "your_api_key_here"}}}, {"id": "minima", "name": "minima", "display_name": "<PERSON><PERSON>", "description": "MCP server for RAG on local files", "repository": {"type": "git", "url": "https://github.com/dmayboroda/minima"}, "homepage": "https://github.com/dmayboroda/minima", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "license": "MPLv2", "categories": ["RAG", "On-Premises", "Containers"], "tags": ["ChatGPT", "Integration", "Local", "Open Source"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/dmayboroda/minima.git", "minima"]}}, "arguments": {"LOCAL_FILES_PATH": {"description": "Specify the root folder for indexing (on your cloud or local pc). Indexing is a recursive process, meaning all documents within subfolders of this root folder will also be indexed. Supported file types: .pdf, .xls, .docx, .txt, .md, .csv.", "required": true, "example": "/Users/<USER>/Downloads/PDFs/"}, "EMBEDDING_MODEL_ID": {"description": "Specify the embedding model to use. Currently, only Sentence Transformer models are supported. Testing has been done with sentence-transformers/all-mpnet-base-v2, but other Sentence Transformer models can be used.", "required": false, "example": "sentence-transformers/all-mpnet-base-v2"}, "EMBEDDING_SIZE": {"description": "Define the embedding dimension provided by the model, which is needed to configure Qdrant vector storage. Ensure this value matches the actual embedding size of the specified EMBEDDING_MODEL_ID.", "required": false, "example": "768"}, "OLLAMA_MODEL": {"description": "Set up the Ollama model, use an ID available on the Ollama site. Please, use LLM model here, not an embedding.", "required": false, "example": "qwen2:0.5b"}, "RERANKER_MODEL": {"description": "Specify the reranker model. Currently, we have tested with BAAI rerankers. You can explore all available rerankers using a specific link.", "required": false, "example": "BAAI/bge-reranker-base"}, "USER_ID": {"description": "Just use your email here, this is needed to authenticate custom GPT to search in your data.", "required": true, "example": "<EMAIL>"}, "PASSWORD": {"description": "Put any password here, this is used to create a firebase account for the email specified above.", "required": true, "example": "password"}}}, {"id": "fastn-ai-unified-api-mcp-server", "name": "fastn-ai-unified-api-mcp-server", "display_name": "Fastn API Server", "description": "A remote, dynamic MCP server with a unified API that connects to 1,000+ tools, actions, and workflows, featuring built-in authentication and monitoring.", "repository": {"type": "git", "url": "https://github.com/fastnai/mcp-fastn"}, "homepage": "https://github.com/fastnai/mcp-fastn", "author": {"name": "fastnai"}, "license": "MIT", "categories": ["API"], "tags": ["<PERSON><PERSON>", "Dynamic Tool Registration", "API-Driven Operations"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/fastnai/mcp-fastn", "fastn-server.py", "--api_key", "${YOUR_API_KEY}", "--space_id", "${YOUR_WORKSPACE_ID}"]}}, "arguments": {"YOUR_API_KEY": {"description": "The API key is required to authenticate and access the Fastn server's features and services.", "required": true, "example": "your_actual_api_key_here"}, "YOUR_WORKSPACE_ID": {"description": "The unique identifier for your workspace in Fastn, which directs the server to the correct environment and settings.", "required": true, "example": "your_actual_workspace_id_here"}}}, {"id": "sentry", "name": "sentry", "display_name": "Sentry", "description": "Retrieving and analyzing issues from Sentry.io", "repository": {"type": "git", "url": "https://github.com/modelcontextprotocol/servers"}, "homepage": "https://github.com/modelcontextprotocol/servers/blob/main/src/sentry", "author": {"name": "modelcontextprotocol"}, "license": "MIT", "categories": ["monitoring", "error-reporting"], "tags": ["sentry", "monitoring", "errors", "debugging"], "examples": [{"title": "Retrieve issue details from Sentry", "description": "Use this command to get detailed information about a specific Sentry issue using its ID or URL.", "prompt": "sentry-issue {issue_id_or_url}"}], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["mcp-server-sentry", "--auth-token", "${YOUR_SENTRY_TOKEN}"]}, "docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "--rm", "mcp/sentry", "--auth-token", "${YOUR_SENTRY_TOKEN}"]}, "python": {"type": "python", "command": "python", "args": ["-m", "mcp_server_sentry", "--auth-token", "${YOUR_SENTRY_TOKEN}"]}}, "arguments": {"YOUR_SENTRY_TOKEN": {"description": "An authentication token required to access your Sentry account and retrieve issue details.", "required": true, "example": "abc123def456"}}}, {"id": "mcp-proxy", "name": "mcp-proxy", "display_name": "MCP Proxy", "description": "Connect to MCP servers that run on SSE transport, or expose stdio servers as an SSE server.", "repository": {"type": "git", "url": "https://github.com/sparfenyuk/mcp-proxy"}, "homepage": "https://github.com/sparfenyuk/mcp-proxy", "author": {"name": "sparfenyuk"}, "license": "MIT", "categories": ["Networking", "Proxy"], "tags": ["proxy", "sse", "stdio"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["mcp-proxy"]}}}, {"id": "dataset-viewer", "name": "dataset-viewer", "display_name": "Dataset Viewer", "description": "Browse and analyze Hugging Face datasets with features like search, filtering, statistics, and data export", "repository": {"type": "git", "url": "https://github.com/privetin/dataset-viewer"}, "homepage": "https://github.com/privetin/dataset-viewer", "author": {"name": "privetin", "url": "https://github.com/privetin"}, "license": "MIT", "categories": ["data", "API"], "tags": ["Hugging Face", "datasets", "data analysis"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/privetin/dataset-viewer", "dataset-viewer"]}}, "examples": [{"title": "Validate a dataset", "description": "Check if a dataset exists and is accessible.", "prompt": "{\"dataset\": \"stanfordnlp/imdb\"}"}, {"title": "Get dataset information", "description": "Retrieve detailed information about a dataset.", "prompt": "{\"dataset\": \"stanfordnlp/imdb\"}"}, {"title": "Search dataset contents", "description": "Search for text within a dataset.", "prompt": "{\"dataset\": \"stanfordnlp/imdb\",\"config\": \"plain_text\",\"split\": \"train\",\"query\": \"great movie\"}"}, {"title": "Filter and sort rows", "description": "Filter rows using SQL-like conditions and sort them.", "prompt": "{\"dataset\": \"stanfordnlp/imdb\",\"config\": \"plain_text\",\"split\": \"train\",\"where\": \"label = 'positive'\",\"orderby\": \"text DESC\",\"page\": 0}"}, {"title": "Get dataset statistics", "description": "Get statistics about a dataset split.", "prompt": "{\"dataset\": \"stanfordnlp/imdb\",\"config\": \"plain_text\",\"split\": \"train\"}"}], "arguments": {"HUGGINGFACE_TOKEN": {"description": "Your Hugging Face API token for accessing private datasets", "required": false, "example": ""}}}, {"id": "intercom", "name": "intercom", "display_name": "Intercom Support Server", "description": "An MCP-compliant server for retrieving customer support tickets from Intercom. This tool enables AI assistants like <PERSON> and Cline to access and analyze your Intercom support tickets.", "repository": {"type": "git", "url": "https://github.com/raoulbia-ai/mcp-server-for-intercom"}, "homepage": "https://github.com/raoulbia-ai/mcp-server-for-intercom", "author": {"name": "raoulbia-ai"}, "license": "Apache-2.0", "categories": ["support", "customer-service"], "tags": ["Intercom", "support-tickets", "API"], "examples": [{"title": "List Tickets Example", "description": "Retrieve support tickets from Intercom between specific dates", "prompt": "{\"startDate\":\"15/01/2025\",\"endDate\":\"21/01/2025\",\"keyword\":\"billing\"}"}], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "mcp-server-for-intercom"], "env": {"INTERCOM_ACCESS_TOKEN": "your-intercom-access-token"}}}, "arguments": {"INTERCOM_ACCESS_TOKEN": {"description": "Your Intercom API token used to authenticate requests to the Intercom API.", "required": true, "example": "your_intercom_api_token"}}}, {"id": "vega-lite", "name": "vega-lite", "display_name": "Vega-Lite Data Visualization", "description": "Generate visualizations from fetched data using the VegaLite format and renderer.", "repository": {"type": "git", "url": "https://github.com/isaacwasserman/mcp-vegalite-server"}, "homepage": "https://github.com/isaacwasserman/mcp-vegalite-server", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "license": "[NOT GIVEN]", "categories": ["data visualization", "Vega-Lite"], "tags": ["visualization", "data", "vega-lite"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/isaacwasserman/mcp-vegalite-server", "--output_type", "png"]}}, "examples": [{"title": "Saving Data", "description": "Use the save_data tool to save a table of data for visualization.", "prompt": "save_data(name='my_table', data=[{'x': 1, 'y': 2}, {'x': 2, 'y': 3}])"}, {"title": "Visualizing Data", "description": "Use the visualize_data tool to visualize saved data using Vega-Lite syntax.", "prompt": "visualize_data(data_name='my_table', vegalite_specification='{\"mark\": \"point\", \"encoding\": {\"x\":{\"field\":\"x\",\"type\":\"quantitative\"},\"y\":{\"field\":\"y\",\"type\":\"quantitative\"}}}')"}]}, {"id": "glean", "name": "glean", "display_name": "Glean", "description": "A server that uses Glean API to search and chat.", "repository": {"type": "git", "url": "https://github.com/longyi1207/glean-mcp-server"}, "homepage": "https://github.com/longyi1207/glean-mcp-server", "author": {"name": "longyi1207", "url": "https://github.com/longyi1207"}, "license": "MIT", "categories": ["api"], "tags": ["glean", "search", "chat", "docker"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/longyi1207/glean-mcp-server"]}}, "arguments": {"GLEAN_API_KEY": {"description": "The API key required to authenticate with the Glean API.", "required": true, "example": "YOUR_API_KEY_HERE"}, "GLEAN_DOMAIN": {"description": "The domain used for the Glean API service operations.", "required": true, "example": "YOUR_DOMAIN_HERE"}}}, {"id": "google-drive", "name": "google-drive", "display_name": "Google Drive", "description": "File access and search capabilities for Google Drive", "repository": {"type": "git", "url": "https://github.com/modelcontextprotocol/servers"}, "homepage": "https://github.com/modelcontextprotocol/servers/blob/main/src/gdrive", "author": {"name": "modelcontextprotocol"}, "license": "MIT", "categories": ["cloud storage", "file management"], "tags": ["google drive", "files", "API"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-gdrive"]}, "docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "--rm", "-v", "mcp-gdrive:/gdrive-server", "-e", "GDRIVE_CREDENTIALS_PATH=/gdrive-server/credentials.json", "mcp/gdrive"]}}, "arguments": {"GDRIVE_CREDENTIALS_PATH": {"description": "Path to the credentials JSON file for Google Drive OAuth.", "required": true, "example": "/gdrive-server/credentials.json"}}}, {"id": "excel", "name": "excel", "display_name": "Excel", "description": "Excel manipulation including data reading/writing, worksheet management, formatting, charts, and pivot table.", "repository": {"type": "git", "url": "https://github.com/haris-musa/excel-mcp-server"}, "homepage": "https://github.com/haris-musa/excel-mcp-server", "author": {"name": "haris-musa"}, "license": "MIT", "categories": ["Excel"], "tags": ["Excel Manipulation", "Python"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/haris-musa/excel-mcp-server", "excel-mcp-server"], "env": {"EXCEL_FILES_PATH": "${EXCEL_FILES_PATH}"}}}, "arguments": {"EXCEL_FILES_PATH": {"description": "Directory where Excel files will be stored.", "required": false, "example": "/path/to/excel/files"}}}, {"id": "opendota", "name": "opendota", "display_name": "OpenDota", "description": "Interact with OpenDota API to retrieve Dota 2 match data, player statistics, and more.", "repository": {"type": "git", "url": "https://github.com/asusevski/opendota-mcp-server"}, "homepage": "https://github.com/asusevski/opendota-mcp-server", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["API", "Gaming"], "tags": ["Dota 2", "API", "Gaming", "Statistics"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["git+https://github.com/asusevski/opendota-mcp-server.git", "src/opendota_server/server"]}}}, {"id": "stripe", "name": "stripe", "display_name": "Stripe Model Context Protocol", "description": "The Stripe Model Context Protocol server allows you to integrate with Stripe APIs through function calling. This protocol supports various tools to interact with different Stripe services.", "repository": {"type": "git", "url": "https://github.com/stripe/agent-toolkit"}, "homepage": "https://github.com/stripe/agent-toolkit/tree/main/modelcontextprotocol", "author": {"name": "stripe"}, "license": "MIT", "categories": ["Payments", "Financial"], "tags": ["stripe", "payments", "customers", "refunds"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@stripe/mcp", "--tools=all", "--api-key=${STRIPE_SECRET_KEY}"]}}, "examples": [{"title": "Create a customer", "description": "Creates a new customer in Stripe.", "prompt": "{\"tool\": \"customer_create\", \"arguments\": {\"email\": \"<EMAIL>\", \"name\": \"<PERSON>\"}}"}, {"title": "Retrieve a customer", "description": "Retrieves details of an existing customer.", "prompt": "{\"tool\": \"customer_retrieve\", \"arguments\": {\"customer_id\": \"cus_123456\"}}"}, {"title": "Create a payment intent", "description": "Creates a payment intent for processing payments.", "prompt": "{\"tool\": \"payment_intent_create\", \"arguments\": {\"amount\": 5000, \"currency\": \"usd\", \"customer\": \"cus_123456\"}}"}, {"title": "Create a refund", "description": "Creates a refund for a charge.", "prompt": "{\"tool\": \"refund_create\", \"arguments\": {\"charge_id\": \"ch_abc123\"}}"}], "arguments": {"STRIPE_SECRET_KEY": {"description": "Your Stripe secret API key required for authenticating requests to the Stripe API.", "required": true, "example": "sk_test_4eC39HqLyjWDarjtT1zdp7dc"}}}, {"id": "unity3d-game-engine", "name": "unity3d-game-engine", "display_name": "Unity3D Game Engine", "description": "An MCP server that enables LLMs to interact with Unity3d Game Engine, supporting access to a variety of the Unit's Editor engine tools (e.g. Console Logs, Test Runner logs, Editor functions, hierarchy state, etc) and executing them as MCP tools or gather them as resources.", "repository": {"type": "git", "url": "https://github.com/CoderGamester/mcp-unity"}, "homepage": "https://github.com/CoderGamester/mcp-unity", "author": {"name": "CoderGamester"}, "license": "MIT", "categories": ["Unity", "AI"], "tags": ["Unity", "Node.js", "TypeScript", "WebSocket", "AI"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/CoderGamester/mcp-unity"], "env": {"UNITY_PORT": "8090"}}}, "examples": [{"title": "Execute Menu Item", "description": "Execute Unity menu items programmatically using MCP Unity.", "prompt": "mcp-unity execute_menu_item"}], "arguments": {"UNITY_PORT": {"description": "Environment variable to set the port number for the Unity MCP Server. This should be set to the desired port for the server to run and connect with the Unity Editor.", "required": false, "example": "8090"}}}, {"id": "cloudinary", "name": "cloudinary", "display_name": "Cloudinary", "description": "Cloudinary Model Context Protocol Server to upload media to Cloudinary and get back the media link and details.", "repository": {"type": "git", "url": "https://github.com/felores/cloudinary-mcp-server"}, "homepage": "https://github.com/felores/cloudinary-mcp-server", "author": {"name": "felores"}, "license": "MIT", "categories": ["Media", "Image Processing"], "tags": ["cloudinary", "images", "videos"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["@felores/cloudinary-mcp-server@latest"], "env": {"CLOUDINARY_CLOUD_NAME": "${CLOUDINARY_CLOUD_NAME}", "CLOUDINARY_API_KEY": "${CLOUDINARY_API_KEY}", "CLOUDINARY_API_SECRET": "${CLOUDINARY_API_SECRET}"}}}, "examples": [{"title": "Upload an Image", "description": "This example demonstrates how to upload an image to Cloudinary.", "prompt": "use_mcp_tool({ server_name: 'cloudinary', tool_name: 'upload', arguments: { file: 'path/to/image.jpg', resource_type: 'image', public_id: 'my-custom-id' }});"}], "arguments": {"CLOUDINARY_CLOUD_NAME": {"description": "Your Cloudinary cloud name, used to identify your account and resources.", "required": true, "example": "my_cloud_name"}, "CLOUDINARY_API_KEY": {"description": "Your Cloudinary API key, used to authenticate requests to the Cloudinary API.", "required": true, "example": "my_api_key"}, "CLOUDINARY_API_SECRET": {"description": "Your Cloudinary API secret, used to authenticate requests and secure your Cloudinary account.", "required": true, "example": "my_api_secret"}}}, {"id": "notion", "name": "notion", "display_name": "Notion", "description": "Notion MCP integration. Search, Read, Update, and Create pages through <PERSON> chat.", "repository": {"type": "git", "url": "https://github.com/v-3/notion-server"}, "homepage": "https://github.com/v-3/notion-server", "author": {"name": "v-3"}, "license": "MIT", "categories": ["Integration", "API", "Notion"], "tags": ["Notion"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/v-3/notion-server"], "env": {"NOTION_API_KEY": "${NOTION_API_KEY}"}}}, "arguments": {"NOTION_API_KEY": {"description": "Your Notion API key for authentication to access data within your Notion workspace.", "required": true, "example": "your_notion_api_key_here"}}}, {"id": "dicom", "name": "dicom", "display_name": "DICOM Model Context Protocol", "description": "An MCP server to query and retrieve medical images and for parsing and reading dicom-encapsulated documents (pdf etc.).", "repository": {"type": "git", "url": "https://github.com/ChristianHinge/dicom-mcp"}, "homepage": "https://github.com/ChristianHinge/dicom-mcp", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ChristianHinge"}, "license": "MIT", "categories": ["DICOM", "Medical", "AI"], "tags": ["DICOM", "Medical Imaging", "AI", "PDF Extraction"], "examples": [{"title": "List available DICOM nodes", "description": "Retrieve and display all configured DICOM nodes and calling AE titles.", "prompt": "list_dicom_nodes()"}], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/ChristianHinge/dicom-mcp", "dicom-mcp", "/path/to/configuration.yaml"]}}}, {"id": "huggingface-spaces", "name": "huggingface-spaces", "display_name": "huggingface-spaces", "description": "Server for using HuggingFace Spaces, supporting Open Source Image, Audio, Text Models and more. Claude Des<PERSON>op mode for easy integration.", "repository": {"type": "git", "url": "https://github.com/evalstate/mcp-hfspace"}, "author": {"name": "evalstate"}, "license": "MIT", "categories": ["Image Generation", "Text-to-Speech", "Speech-to-Text"], "tags": ["Hugging Face", "<PERSON>"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@llmindset/mcp-hfspace"]}}, "examples": [{"title": "Image Generation Example", "description": "Using mcp-hfspace to generate images.", "prompt": "Use shuttleai/shuttle-3.1-aesthetic to create an image."}, {"title": "Text-to-Speech Example", "description": "Using mcp-hfspace to convert text to speech.", "prompt": "Create an audio file from the text 'Hello, world!'."}, {"title": "Speech-to-Text Example", "description": "Using mcp-hfspace to transcribe audio to text.", "prompt": "Transcribe the audio file 'sample_audio.wav'."}, {"title": "Vision Model Example", "description": "Using mcp-hfspace to analyze images.", "prompt": "Analyze the image file 'test_image.jpg'."}], "homepage": "https://github.com/evalstate/mcp-hfspace", "arguments": {"CLAUDE_DESKTOP_MODE": {"description": "Enables or disables the Claude Desktop Mode for the server.", "required": false, "example": "false"}}}, {"id": "hubspot", "name": "hubspot", "display_name": "HubSpot CRM Integration", "description": "HubSpot CRM integration for managing contacts and companies. Create and retrieve CRM data directly through Claude chat.", "repository": {"type": "git", "url": "https://github.com/buryhuang/mcp-hubspot"}, "homepage": "https://github.com/buryhuang/mcp-hubspot", "author": {"name": "bury<PERSON>ng"}, "license": "MIT", "categories": ["CRM", "Integration"], "tags": ["HubSpot", "API", "AI", "CRM", "Integration"], "installations": {"docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "--rm", "-e", "HUBSPOT_ACCESS_TOKEN=${HUBSPOT_ACCESS_TOKEN}", "buryhuang/mcp-hubspot:latest"], "env": {"HUBSPOT_ACCESS_TOKEN": "${HUBSPOT_ACCESS_TOKEN}"}}}, "examples": [{"title": "Create HubSpot contacts from LinkedIn", "description": "This prompt allows you to create contacts in HubSpot by parsing information from a LinkedIn profile.", "prompt": "Create HubSpot contacts and companies from following:\n\n<PERSON>\nSoftware Engineer at Tech Corp\nSan Francisco Bay Area • 500+ connections\n\nExperience\nTech Corp\nSoftware Engineer\nJan 2020 - Present · 4 yrs\nSan Francisco, California\n\nPrevious Company Inc.\nSenior Developer\n2018 - 2020 · 2 yrs\n\nEducation\nUniversity of California, Berkeley\nComputer Science, BS\n2014 - 2018"}, {"title": "Get latest company activities", "description": "Use this prompt to get the latest activities related to your company in HubSpot.", "prompt": "What's happening latestly with my pipeline?"}], "arguments": {"HUBSPOT_ACCESS_TOKEN": {"description": "The HubSpot access token required for authenticating API requests to HubSpot.", "required": true, "example": "your_access_token_here"}}}, {"id": "ticketmaster", "name": "ticketmaster", "display_name": "Ticketmaster", "description": "Search for events, venues, and attractions through the Ticketmaster Discovery API", "repository": {"type": "git", "url": "https://github.com/delorenj/mcp-server-ticketmaster"}, "homepage": "https://github.com/delorenj/mcp-server-ticketmaster", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["Events", "API"], "tags": ["ticketmaster", "events", "venues", "attractions"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@delorenj/mcp-server-ticketmaster"], "env": {"TICKETMASTER_API_KEY": "${TICKETMASTER_API_KEY}"}}}, "examples": [{"title": "Structured JSON Output", "description": "Example of structured JSON output for searching events.", "prompt": "<use_mcp_tool>\n<server_name>ticketmaster</server_name>\n<tool_name>search_ticketmaster</tool_name>\n<arguments>\n{\n  \"type\": \"event\",\n  \"keyword\": \"concert\",\n  \"startDate\": \"2025-02-01\",\n  \"endDate\": \"2025-02-28\",\n  \"city\": \"New York\",\n  \"stateCode\": \"NY\"\n}\n</arguments>\n</use_mcp_tool>"}, {"title": "Human-Readable Text Output", "description": "Example of human-readable text output for searching events.", "prompt": "<use_mcp_tool>\n<server_name>ticketmaster</server_name>\n<tool_name>search_ticketmaster</tool_name>\n<arguments>\n{\n  \"type\": \"event\",\n  \"keyword\": \"concert\",\n  \"startDate\": \"2025-02-01\",\n  \"endDate\": \"2025-02-28\",\n  \"city\": \"New York\",\n  \"stateCode\": \"NY\",\n  \"format\": \"text\"\n}\n</arguments>\n</use_mcp_tool>"}], "arguments": {"TICKETMASTER_API_KEY": {"description": "API key required to access the Ticketmaster Discovery API.", "required": true, "example": "your-api-key-here"}}}, {"id": "figma", "name": "figma", "display_name": "Figma", "description": "Give your coding agent direct access to Figma file data, helping it one-shot design implementation.", "repository": {"type": "git", "url": "https://github.com/GLips/Figma-Context-MCP"}, "homepage": "https://github.com/GLips/Figma-Context-MCP", "author": {"name": "GLips"}, "license": "MIT", "categories": ["Development Tools", "Design Tools"], "tags": ["Figma", "AI"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "figma-developer-mcp", "--figma-api-key=${FIGMA_API_KEY}", "--st<PERSON>"]}}, "arguments": {"FIGMA_API_KEY": {"description": "Your Figma API access token (required)", "required": true, "example": "<your-figma-api-key>"}}}, {"id": "starwind-ui", "name": "starwind-ui", "display_name": "Starwind UI", "description": "This MCP provides relevant commands, documentation, and other information to allow LLMs to take full advantage of Starwind UI's open source Astro components.", "repository": {"type": "git", "url": "https://github.com/Boston343/starwind-ui-mcp"}, "homepage": "https://github.com/Boston343/starwind-ui-mcp/", "author": {"name": "Boston343"}, "license": "MIT", "categories": ["Server", "TypeScript"], "tags": ["Starwind", "Developer Tools", "AI", "Components"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/Boston343/starwind-ui-mcp/"]}}}, {"id": "time", "name": "time", "display_name": "Time", "description": "A Model Context Protocol server that provides time and timezone conversion capabilities. It automatically detects the system's timezone and offers tools for getting current time and converting between timezones.", "repository": {"type": "git", "url": "https://github.com/modelcontextprotocol/servers"}, "homepage": "https://github.com/modelcontextprotocol/servers/tree/main/src/time#readme", "author": {"name": "MCP Team"}, "license": "MIT", "categories": ["utility", "time"], "tags": ["time", "timezone", "date", "converter"], "arguments": {"TZ": {"description": "Environment variable to override the system's default timezone", "required": false, "example": "America/New_York"}}, "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["mcp-server-time"], "env": {"TZ": "${TZ}"}, "description": "Install and run using uvx (recommended)", "recommended": true}, "python": {"type": "python", "command": "python", "args": ["-m", "mcp_server_time"], "env": {"TZ": "${TZ}"}, "description": "Run with Python module (requires pip install)"}, "docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "--rm", "-e", "TZ", "mcp/time"], "env": {"TZ": "${TZ}"}, "description": "Run with <PERSON><PERSON>"}}, "examples": [{"title": "Current time", "description": "Get the current time in a specific timezone", "prompt": "What time is it in Tokyo right now?"}, {"title": "Time conversion", "description": "Convert time between timezones", "prompt": "Convert 3:30 PM EST to Paris time."}]}, {"id": "ableton-live", "name": "ableton-live", "display_name": "Ableton Live", "description": "an MCP server to control Ableton Live.", "repository": {"type": "git", "url": "https://github.com/<PERSON>-<PERSON><PERSON>/ableton-live-mcp-server"}, "homepage": "https://github.com/<PERSON>-<PERSON><PERSON>/ableton-live-mcp-server", "author": {"name": "<PERSON>"}, "license": "MIT", "categories": ["Audio", "Live Performance"], "tags": ["Ableton Live", "OSC", "Music"], "installations": {"custom": {"type": "python", "command": "python", "args": ["mcp_ableton_server.py"], "description": "Run with Python module (requires git clone)"}}, "examples": [{"title": "Prepare a rock band set for recording", "description": "In <PERSON> desktop, ask <PERSON> to prepare a set to record a rock band.", "prompt": "_Prepare a set to record a rock band_"}, {"title": "Set input routing for tracks", "description": "Set the input routing channel of all tracks that have 'voice' in their name to Ext. In 2.", "prompt": "_Set the input routing channel of all tracks that have 'voice' in their name to Ext. In 2_"}]}, {"id": "pandoc", "name": "pandoc", "display_name": "Pandoc Document Conversion", "description": "MCP server for seamless document format conversion using Pandoc, supporting Markdown, HTML, PDF, DOCX (.docx), csv and more.", "repository": {"type": "git", "url": "https://github.com/vivekVells/mcp-pandoc"}, "homepage": "https://github.com/vivekVells/mcp-pandoc", "author": {"name": "v<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["Document Conversion"], "tags": ["pandoc", "document", "conversion"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["mcp-pandoc"]}}, "examples": [{"title": "Convert Markdown to PDF", "description": "Converts Markdown content to PDF format and saves it to the specified path.", "prompt": "Convert /path/to/input.md to PDF and save as /path/to/output.pdf"}, {"title": "Convert Content Directly", "description": "Converts a string of content directly to a specific output format.", "prompt": "Convert this text to PDF and save as /path/to/document.pdf"}]}, {"id": "aws-athena", "name": "aws-athena", "display_name": "AWS Athena", "description": "A MCP server for AWS Athena to run SQL queries on Glue Catalog.", "repository": {"type": "git", "url": "https://github.com/lishenxydlgzs/aws-athena-mcp"}, "homepage": "https://github.com/lishenxydlgzs/aws-athena-mcp", "author": {"name": "lishenxydlgzs"}, "license": "MIT", "categories": ["database", "AWS"], "tags": ["athena", "sql", "aws"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@lishenxydlgzs/aws-athena-mcp"], "env": {"OUTPUT_S3_PATH": "${OUTPUT_S3_PATH}", "AWS_REGION": "${AWS_REGION}", "AWS_PROFILE": "${AWS_PROFILE}", "AWS_ACCESS_KEY_ID": "${AWS_ACCESS_KEY_ID}", "AWS_SECRET_ACCESS_KEY": "${AWS_SECRET_ACCESS_KEY}", "AWS_SESSION_TOKEN": "${AWS_SESSION_TOKEN}", "QUERY_TIMEOUT_MS": "${QUERY_TIMEOUT_MS}", "MAX_RETRIES": "${MAX_RETRIES}", "RETRY_DELAY_MS": "${RETRY_DELAY_MS}"}}}, "examples": [{"title": "Show All Databases", "description": "Lists all databases in Athena", "prompt": "{\"database\": \"default\", \"query\": \"SHOW DATABASES\"}"}, {"title": "List Tables in a Database", "description": "Shows all tables in the default database", "prompt": "{\"database\": \"default\", \"query\": \"SHOW TABLES\"}"}, {"title": "Get Table Schema", "description": "Fetches the schema of the asin_sitebestimg table", "prompt": "{\"database\": \"default\", \"query\": \"DESCRIBE default.asin_sitebestimg\"}"}, {"title": "Table Rows Preview", "description": "Shows some rows from my_database.mytable", "prompt": "{\"database\": \"my_database\", \"query\": \"SELECT * FROM my_table LIMIT 10\", \"maxRows\": 10}"}, {"title": "Advanced Query with Filtering and Aggregation", "description": "Finds the average price by category for in-stock products", "prompt": "{\"database\": \"my_database\", \"query\": \"SELECT category, COUNT(*) as count, AVG(price) as avg_price FROM products WHERE in_stock = true GROUP BY category ORDER BY count DESC\", \"maxRows\": 100}"}], "arguments": {"OUTPUT_S3_PATH": {"description": "S3 bucket path for saving Athena query results.", "required": true, "example": "s3://your-bucket/athena-results/"}, "AWS_REGION": {"description": "The AWS region to use for Athena queries, defaults to AWS CLI default region.", "required": false, "example": "us-east-1"}, "AWS_PROFILE": {"description": "AWS CLI profile to use, defaults to 'default' profile.", "required": false, "example": "default"}, "AWS_ACCESS_KEY_ID": {"description": "AWS access key for authentication, if not using IAM role or environment variables.", "required": false, "example": ""}, "AWS_SECRET_ACCESS_KEY": {"description": "AWS secret key for authentication, if not using IAM role or environment variables.", "required": false, "example": ""}, "AWS_SESSION_TOKEN": {"description": "Session token for temporary AWS credentials, if using temporary access.", "required": false, "example": ""}, "QUERY_TIMEOUT_MS": {"description": "Timeout setting for queries in milliseconds (default: 300000 ms).", "required": false, "example": "300000"}, "MAX_RETRIES": {"description": "Number of retry attempts for failed queries (default: 100).", "required": false, "example": "100"}, "RETRY_DELAY_MS": {"description": "Delay between retry attempts in milliseconds (default: 500 ms).", "required": false, "example": "500"}}}, {"id": "basic-memory", "name": "basic-memory", "display_name": "Basic Memory", "description": "Local-first knowledge management system that builds a semantic graph from Markdown files, enabling persistent memory across conversations with LLMs.", "repository": {"type": "git", "url": "https://github.com/basicmachines-co/basic-memory"}, "homepage": "https://github.com/basicmachines-co/basic-memory", "author": {"name": "basicmachines-co"}, "license": "AGPL-3.0", "categories": ["Knowledge Management", "AI", "<PERSON><PERSON>"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["basic-memory", "mcp", "--project", "${PROJECT_NAME}"]}}, "tags": ["LLM", "<PERSON><PERSON>", "Knowledge Base"], "arguments": {"PROJECT_NAME": {"description": "The name of the project to be associated with Basic Memory in the Claude Desktop configuration.", "required": true, "example": "my_project"}}}, {"id": "deepseek-r1", "name": "deepseek-r1", "display_name": "Deepseek R1", "description": "A Model Context Protocol (MCP) server implementation connecting <PERSON> with DeepSeek's language models (R1/V3)", "repository": {"type": "git", "url": "https://github.com/66julienmartin/MCP-server-Deepseek_R1"}, "homepage": "https://github.com/66julienmartin/MCP-server-Deepseek_R1", "author": {"name": "66<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/66julienmartin"}, "license": "MIT", "categories": ["Deepseek"], "tags": ["Deepseek", "LLM"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/66julienmartin/MCP-server-Deepseek_R1"], "env": {"DEEPSEEK_API_KEY": "${DEEPSEEK_API_KEY}"}}}, "arguments": {"DEEPSEEK_API_KEY": {"description": "API key for authenticating with the Deepseek service.", "required": true, "example": "your-api-key"}}}, {"id": "oceanbase", "name": "oceanbase", "display_name": "OceanBase", "description": "(by yuanoOo) A Model Context Protocol (MCP) server that enables secure interaction with OceanBase databases.", "repository": {"type": "git", "url": "https://github.com/yuanoOo/oceanbase_mcp_server"}, "homepage": "https://github.com/yuanoOo/oceanbase_mcp_server", "author": {"name": "yuanoOo"}, "license": "Apache-2.0", "categories": ["Database", "Server"], "tags": ["OceanBase", "SQL", "Security"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/yuanoOo/oceanbase_mcp_server.git", "oceanbase_mcp_server"], "env": {"OB_HOST": "${OB_HOST}", "OB_PORT": "${OB_PORT}", "OB_USER": "${OB_USER}", "OB_PASSWORD": "${OB_PASSWORD}", "OB_DATABASE": "${OB_DATABASE}"}}}, "arguments": {"OB_HOST": {"description": "Database host for connecting to the OceanBase server.", "required": true, "example": "localhost"}, "OB_PORT": {"description": "Optional: Database port to connect to OceanBase, defaults to 2881 if not specified.", "required": false, "example": "2881"}, "OB_USER": {"description": "Username for authenticating with the OceanBase database.", "required": true, "example": "your_username"}, "OB_PASSWORD": {"description": "Password for the specified database user.", "required": true, "example": "your_password"}, "OB_DATABASE": {"description": "Name of the OceanBase database to connect to.", "required": true, "example": "your_database"}}}, {"id": "mcp-installer", "name": "mcp-installer", "display_name": "Installer", "description": "This server is a server that installs other MCP servers for you.", "repository": {"type": "git", "url": "https://github.com/anaisbetts/mcp-installer"}, "homepage": "https://github.com/anaisbetts/mcp-installer", "author": {"name": "an<PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["Installer"], "tags": ["installer", "server"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["@anaisbetts/mcp-installer"]}}, "examples": [{"title": "Install MCP server", "description": "Install the MCP server named mcp-server-fetch", "prompt": "<PERSON> <PERSON>, install the MCP server named mcp-server-fetch"}, {"title": "Install server with arguments", "description": "Install the @modelcontextprotocol/server-filesystem package as an MCP server with specific arguments", "prompt": "Hey <PERSON>, install the @modelcontextprotocol/server-filesystem package as an MCP server. Use ['/Users/<USER>/Desktop'] for the arguments"}, {"title": "Install from directory", "description": "Install the MCP server from a specific directory", "prompt": "<PERSON> <PERSON>, please install the MCP server at /Users/<USER>/code/mcp-youtube, I'm too lazy to do it myself."}, {"title": "Set environment variable", "description": "Install the server @modelcontextprotocol/server-github with an environment variable", "prompt": "Install the server @modelcontextprotocol/server-github. Set the environment variable GITHUB_PERSONAL_ACCESS_TOKEN to '**********'"}]}, {"id": "google-calendar", "name": "google-calendar", "display_name": "Google Calendar", "description": "Google Calendar MCP Server for managing Google calendar events. Also supports searching for events by attributes like title and location.", "repository": {"type": "git", "url": "https://github.com/nspady/google-calendar-mcp"}, "homepage": "https://github.com/nspady/google-calendar-mcp", "author": {"name": "nspady"}, "license": "MIT", "categories": ["Productivity", "Calendar", "Integration"], "tags": ["Google Calendar", "event management"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/nspady/google-calendar-mcp"]}}, "examples": [{"title": "Add Event from Screenshot", "description": "Add events from screenshots and images", "prompt": "Add this event to my calendar based on the attached screenshot."}, {"title": "Check Upcoming Events", "description": "Discover upcoming events outside usual routines", "prompt": "What events do I have coming up this week that aren't part of my usual routine?"}, {"title": "Check Attendance", "description": "Identify events with unaccepted invitations", "prompt": "Which events tomorrow have attendees who have not accepted the invitation?"}, {"title": "Auto Coordinate Events", "description": "Create events based on the available times provided", "prompt": "Here's some available that was provided to me by someone. Take a look at the available times and create an event that is free on my work calendar."}, {"title": "Check Availability", "description": "Provide your availability checking both calendars", "prompt": "Please provide availability looking at both my personal and work calendar for this upcoming week."}]}, {"id": "cryptopanic-mcp-server", "name": "cryptopanic-mcp-server", "display_name": "CryptoPanic News Server", "description": "Providing latest cryptocurrency news to AI agents, powered by CryptoPanic.", "repository": {"type": "git", "url": "https://github.com/kukapay/cryptopanic-mcp-server"}, "homepage": "https://github.com/kukapay/cryptopanic-mcp-server", "author": {"name": "kukapay", "url": "https://github.com/kukapay"}, "license": "MIT", "examples": [{"title": "Fetch Cryptocurrency News", "description": "Get the latest news articles on cryptocurrencies.", "prompt": "get_crypto_news(kind='news', num_pages=1)"}], "categories": ["Crypto", "News"], "tags": ["cryptocurrency", "news", "CryptoPanic"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/kukapay/cryptopanic-mcp-server", "main.py"], "env": {"CRYPTOPANIC_API_KEY": "${CRYPTOPANIC_API_KEY}"}}}, "arguments": {"CRYPTOPANIC_API_KEY": {"description": "API key to access CryptoPanic services. This key is necessary to authenticate requests made to the CryptoPanic API.", "required": true, "example": "your_api_key_here"}}}, {"id": "ghost", "name": "ghost", "display_name": "Ghost", "description": "A Model Context Protocol (MCP) server for interacting with Ghost CMS through LLM interfaces like Claude.", "repository": {"type": "git", "url": "https://github.com/MFYDev/ghost-mcp"}, "homepage": "https://github.com/MFYDev/ghost-mcp", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["CMS"], "tags": ["Ghost", "CMS", "Admin API"], "examples": [{"title": "List Posts", "description": "List blog posts with pagination.", "prompt": "ghost(action=\"list_posts\", params={\"format\": \"text\", \"page\": 1, \"limit\": 15})"}, {"title": "Search Posts by Title", "description": "Search for posts by title.", "prompt": "ghost(action=\"search_posts_by_title\", params={\"query\": \"Welcome\", \"exact\": False})"}, {"title": "Create a Post", "description": "Create a new post.", "prompt": "ghost(action=\"create_post\", params={\"post_data\": {\"title\": \"New Post via MCP\",\"status\": \"draft\",\"lexical\": \"{\\\"root\\\":{\\\"children\\\":[{\\\"children\\\":[{\\\"detail\\\":0,\\\"format\\\":0,\\\"mode\\\":\\\"normal\\\",\\\"style\\\":\\\"\\\",\\\"text\\\":\\\"Hello World\\\",\\\"type\\\":\\\"text\\\",\\\"version\\\":1}],\\\"direction\\\":\\\"ltr\\\",\\\"format\\\":\\\"\\\",\\\"indent\\\":0,\\\"type\\\":\\\"paragraph\\\",\\\"version\\\":1}],\\\"direction\\\":\\\"ltr\\\",\\\"format\\\":\\\"\\\",\\\"indent\\\":0,\\\"type\\\":\\\"root\\\",\\\"version\\\":1}}\"}}"}], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/MFYDev/ghost-mcp", "src/main.py"], "env": {"GHOST_API_URL": "${GHOST_API_URL}", "GHOST_STAFF_API_KEY": "${GHOST_STAFF_API_KEY}"}}}, "arguments": {"GHOST_API_URL": {"description": "Your Ghost Admin API URL", "required": true, "example": "https://yourblog.com"}, "GHOST_STAFF_API_KEY": {"description": "Your Ghost Staff API key", "required": true, "example": "your_staff_api_key"}}}, {"id": "snowflake", "name": "snowflake", "display_name": "Snowflake", "description": "This MCP server enables LLMs to interact with Snowflake databases, allowing for secure and controlled data operations.", "repository": {"type": "git", "url": "https://github.com/isaacwasserman/mcp-snowflake-server"}, "homepage": "https://github.com/isaacwasserman/mcp-snowflake-server", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "license": "NOT GIVEN", "categories": ["database"], "tags": ["snowflake", "sql", "database"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["mcp_snowflake_server", "--account", "${ACCOUNT}", "--warehouse", "${WAREHOUSE}", "--user", "${USER}", "--password", "${PASSWORD}", "--role", "${ROLE}", "--database", "${DATABASE}", "--schema", "${SCHEMA}"]}}, "arguments": {"ACCOUNT": {"description": "The Snowflake account name to connect to.", "required": true, "example": "your_account_name"}, "WAREHOUSE": {"description": "The name of the virtual warehouse to be used for the session.", "required": true, "example": "your_warehouse_name"}, "USER": {"description": "The username to authenticate with <PERSON><PERSON><PERSON>.", "required": true, "example": "your_username"}, "PASSWORD": {"description": "The password for the specified user.", "required": true, "example": "your_password"}, "ROLE": {"description": "The role to be assumed during the session.", "required": true, "example": "your_role_name"}, "DATABASE": {"description": "The name of the Snowflake database to connect to.", "required": true, "example": "your_database_name"}, "SCHEMA": {"description": "The schema within the database where queries will be executed.", "required": true, "example": "your_schema_name"}}}, {"id": "rquest", "name": "rquest", "display_name": "Rquest", "description": "An MCP server providing realistic browser-like HTTP request capabilities with accurate TLS/JA3/JA4 fingerprints for bypassing anti-bot measures.", "repository": {"type": "git", "url": "https://github.com/xxxbrian/mcp-rquest"}, "homepage": "https://github.com/xxxbrian/mcp-rquest", "author": {"name": "xxxbrian"}, "license": "MIT", "categories": ["HTTP", "Browser Emulation", "Document Conversion"], "tags": ["http", "request", "llm", "browser", "emulation", "pdf", "markdown"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["mcp-rquest"]}, "python": {"type": "python", "command": "python", "args": ["-m", "mcp-rquest"]}}, "examples": [{"title": "Convert HTML or PDF to Markdown", "description": "Use the get_stored_response_with_markdown tool to convert HTML or PDF responses to Markdown for better processing by LLMs.", "prompt": "get_stored_response_with_markdown('document.pdf')"}]}, {"id": "neo4j", "name": "neo4j", "display_name": "Neo4j Server", "description": "A community built server that interacts with Neo4j Graph Database.", "repository": {"type": "git", "url": "https://github.com/da-okazaki/mcp-neo4j-server"}, "homepage": "https://github.com/da-okazaki/mcp-neo4j-server", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["database", "Neo4j"], "tags": ["neo4j", "database"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@alanse/mcp-neo4j-server"], "env": {"NEO4J_URI": "${NEO4J_URI}", "NEO4J_USERNAME": "${NEO4J_USERNAME}", "NEO4J_PASSWORD": "${NEO4J_PASSWORD}"}}}, "examples": [{"title": "Querying Data", "description": "Ask questions about the data, e.g., 'Show me all employees in the Sales department'.", "prompt": "User: \"Show me all employees in the Sales department\""}, {"title": "Creating Data", "description": "Instruct the bot to create new entities, e.g., 'Add a new person named <PERSON> who is 30 years old'.", "prompt": "User: \"Add a new person named <PERSON> who is 30 years old\""}, {"title": "Creating Relationships", "description": "Request to establish relationships between entities, e.g., 'Make <PERSON> friends with <PERSON>'.", "prompt": "User: \"Make <PERSON> friends with <PERSON>\""}, {"title": "Complex Operations", "description": "Perform comprehensive queries like 'Find all products purchased by customers who live in New York'.", "prompt": "User: \"Find all products purchased by customers who live in New York\""}], "arguments": {"NEO4J_URI": {"description": "Neo4j database URI (default: bolt://localhost:7687)", "required": false, "example": "bolt://localhost:7687"}, "NEO4J_USERNAME": {"description": "Neo4j username (default: neo4j)", "required": false, "example": "neo4j"}, "NEO4J_PASSWORD": {"description": "Neo4j password", "required": true}}}, {"id": "discord", "name": "discord", "display_name": "Discord", "description": "A MCP server to connect to Discord guilds through a bot and read and write messages in channels", "repository": {"type": "git", "url": "https://github.com/v-3/discordmcp"}, "homepage": "https://github.com/v-3/discordmcp", "author": {"name": "v-3", "url": "https://github.com/v-3"}, "license": "MIT", "categories": ["Communication", "Protocol"], "tags": ["Discord", "LLM", "Bot"], "examples": [{"title": "Read Messages", "description": "Fetch the last 5 messages from a channel.", "prompt": "{\"channel\": \"general\", \"limit\": 5}"}, {"title": "Send Message", "description": "Send a message to the specified channel.", "prompt": "{\"channel\": \"announcements\", \"message\": \"Meeting starts in 10 minutes\"}"}], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/v-3/discordmcp"], "env": {"DISCORD_TOKEN": "${DISCORD_TOKEN}"}}}, "arguments": {"DISCORD_TOKEN": {"description": "The Discord bot token required for authentication and to interact with Discord's API.", "required": true, "example": "your_discord_bot_token_here"}}}, {"id": "airflow", "name": "airflow", "display_name": "Apache Airflow", "description": "A MCP Server that connects to [Apache Airflow](https://airflow.apache.org/) using official python client.", "repository": {"type": "git", "url": "https://github.com/yangkyeongmo/mcp-server-apache-airflow"}, "homepage": "https://github.com/yangkyeongmo/mcp-server-apache-airflow", "author": {"name": "ya<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["Data Pipeline", "Workflow Automation"], "tags": ["Apache Airflow", "DAG", "Workflow", "Data Pipeline"], "arguments": {"AIRFLOW_HOST": {"description": "URL of your Apache Airflow instance", "required": true, "example": "https://your-airflow-host:8080"}, "AIRFLOW_USERNAME": {"description": "Username for authenticating with Airflow", "required": true, "example": "admin"}, "AIRFLOW_PASSWORD": {"description": "Password for authenticating with Airflow", "required": true, "example": "your_secure_password"}}, "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["mcp-server-apache-airflow"], "env": {"AIRFLOW_HOST": "${AIRFLOW_HOST}", "AIRFLOW_USERNAME": "${AIRFLOW_USERNAME}", "AIRFLOW_PASSWORD": "${AIRFLOW_PASSWORD}"}}}}, {"id": "volcengine-tos", "name": "volcengine-tos", "display_name": "VolcEngine TOS", "description": "A sample MCP server for VolcEngine TOS that flexibly get objects from TOS.", "repository": {"type": "git", "url": "https://github.com/dinghuazhou/sample-mcp-server-tos"}, "author": {"name": "dinghuazhou"}, "license": "MIT", "categories": ["Data Retrieval"], "tags": ["TOS", "Volcengine", "Data"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["tos-mcp-server"]}}, "examples": [{"title": "List Buckets", "description": "Returns a list of all buckets owned by the authenticated sender of the request", "prompt": "ListBuckets"}, {"title": "List Objects in a Bucket", "description": "Returns some or all (up to 1,000) of the objects in a bucket with each request", "prompt": "ListObjectsV2"}, {"title": "Get an Object", "description": "Retrieves an object from volcengine TOS.", "prompt": "GetObject"}], "homepage": "https://github.com/dinghuazhou/sample-mcp-server-tos"}, {"id": "opencti", "name": "opencti", "display_name": "OpenCTI", "description": "Interact with OpenCTI platform to retrieve threat intelligence data including reports, indicators, malware and threat actors.", "repository": {"type": "git", "url": "https://github.com/Spathodea-Network/opencti-mcp"}, "homepage": "https://github.com/Spathodea-Network/opencti-mcp", "author": {"name": "Spathodea-Network"}, "license": "MIT", "categories": ["Threat Intelligence", "Cyber Security"], "tags": ["OpenCTI", "Threat Intelligence"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/Spathodea-Network/opencti-mcp"], "env": {"OPENCTI_URL": "${OPENCTI_URL}", "OPENCTI_TOKEN": "${OPENCTI_TOKEN}"}}}, "examples": [{"title": "Get Latest Reports", "description": "Retrieves the most recent threat intelligence reports.", "prompt": "{ \"name\": \"get_latest_reports\", \"arguments\": { \"first\": 10 } }"}, {"title": "Search Malware", "description": "Searches for malware information in the OpenCTI database.", "prompt": "{ \"name\": \"search_malware\", \"arguments\": { \"query\": \"ransomware\" } }"}, {"title": "User Management - List Users", "description": "Lists all users in the system.", "prompt": "{ \"name\": \"list_users\", \"arguments\": {} }"}], "arguments": {"OPENCTI_URL": {"description": "Your OpenCTI instance URL", "required": true}, "OPENCTI_TOKEN": {"description": "Your OpenCTI API token", "required": true}}}, {"id": "arang<PERSON><PERSON>", "name": "arang<PERSON><PERSON>", "display_name": "ArangoDB", "description": "MCP Server that provides database interaction capabilities through [ArangoDB](https://arangodb.com/).", "repository": {"type": "git", "url": "https://github.com/ravenwits/mcp-server-arangodb"}, "homepage": "https://github.com/ravenwits/mcp-server-arangodb", "author": {"name": "raven<PERSON>ts"}, "license": "MIT", "categories": ["Database"], "tags": ["ArangoDB", "TypeScript"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/ravenwits/mcp-server-arangodb"], "description": "Run with npx (requires npm install)"}}, "examples": [{"title": "List all collections", "description": "Query to list all collections in the database.", "prompt": "{}"}, {"title": "Insert a new document", "description": "Insert a new document into the 'users' collection.", "prompt": "{\"collection\": \"users\", \"document\": {\"name\": \"<PERSON>\", \"email\": \"<EMAIL>\"}}"}, {"title": "Update a document", "description": "Update a document in the 'users' collection by key.", "prompt": "{\"collection\": \"users\", \"key\": \"123456\", \"update\": {\"name\": \"<PERSON>\"}}"}, {"title": "Remove a document", "description": "Remove a document from the 'users' collection by key.", "prompt": "{\"collection\": \"users\", \"key\": \"123456\"}}"}, {"title": "Backup database collections", "description": "Backup collections to a specified directory.", "prompt": "{\"outputDir\": \"./backup\"}"}], "arguments": {"ARANGO_URL": {"description": "ArangoDB server URL (note: 8529 is the default port for ArangoDB for local development)", "required": true}, "ARANGO_DATABASE": {"description": "Database name", "required": true}, "ARANGO_USERNAME": {"description": "Database user", "required": true}, "ARANGO_PASSWORD": {"description": "Database password", "required": true}}}, {"id": "elasticsearch", "name": "elasticsearch", "display_name": "Elasticsearch", "description": "MCP server implementation that provides Elasticsearch interaction.", "repository": {"type": "git", "url": "https://github.com/cr7258/elasticsearch-mcp-server"}, "homepage": "https://github.com/cr7258/elasticsearch-mcp-server", "author": {"name": "cr7258"}, "license": "Apache License Version 2.0", "categories": ["Elasticsearch", "Server"], "tags": ["elasticsearch", "server"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["elasticsearch-mcp-server"], "env": {"ELASTIC_HOST": "${ELASTIC_HOST}", "ELASTIC_USERNAME": "${ELASTIC_USERNAME}", "ELASTIC_PASSWORD": "${ELASTIC_PASSWORD}"}}}, "arguments": {"ELASTIC_HOST": {"description": "The host URL of the Elasticsearch server.", "required": true, "example": "https://localhost:9200"}, "ELASTIC_USERNAME": {"description": "The username for authenticating with the Elasticsearch server.", "required": true, "example": "elastic"}, "ELASTIC_PASSWORD": {"description": "The password for authenticating with the Elasticsearch server.", "required": true, "example": "test123"}}}, {"id": "goal-story", "name": "goal-story", "display_name": "Goal Story", "description": "a Goal Tracker and Visualization Tool for personal and professional development.", "repository": {"type": "git", "url": "https://github.com/hichana/goalstory-mcp"}, "homepage": "https://github.com/hichana/goalstory-mcp", "author": {"name": "<PERSON><PERSON>a"}, "license": "MIT", "categories": ["Productivity", "GoalStory", "Goal Management"], "tags": ["goal tracking", "storytelling", "AI"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "goalstory-mcp", "https://prod-goalstory-rqc2.encr.app", "${YOUR_API_KEY}"]}}, "arguments": {"YOUR_API_KEY": {"description": "The API key required to authenticate your requests to the Goal Story service.", "required": true, "example": "abcdefgh12345678"}}}, {"id": "heurist-mesh-agent", "name": "heurist-mesh-agent", "display_name": "Mesh Agent", "description": "Access specialized web3 AI agents for blockchain analysis, smart contract security, token metrics, and blockchain interactions through the [Heurist Mesh network](https://github.com/heurist-network/heurist-agent-framework/tree/main/mesh).", "repository": {"type": "git", "url": "https://github.com/heurist-network/heurist-mesh-mcp-server"}, "homepage": "https://github.com/heurist-network/heurist-mesh-mcp-server", "author": {"name": "Heurist Network"}, "license": "MIT", "categories": ["Web3", "Blockchain", "AI"], "tags": ["Heurist", "Agent Framework", "Blockchain Tools"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/heurist-network/heurist-mesh-mcp-server/mesh_mcp_server", "mesh-tool-server"], "env": {"HEURIST_API_KEY": "${HEURIST_API_KEY}"}}}, "arguments": {"HEURIST_API_KEY": {"description": "API key for accessing the Heurist services.", "required": true, "example": "your-api-key-here"}}}, {"id": "json", "name": "json", "display_name": "JSON Model Context Protocol", "description": "JSON handling and processing server with advanced query capabilities using JSONPath syntax and support for array, string, numeric, and date operations.", "repository": {"type": "git", "url": "https://github.com/GongRzhe/JSON-MCP-Server"}, "homepage": "https://github.com/GongRzhe/JSON-MCP-Server", "author": {"name": "GongRzhe"}, "license": "MIT", "categories": ["data manipulation", "server"], "tags": ["json", "data querying", "standardized tools"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["@gongrzhe/server-json-mcp@1.0.3"]}}}, {"id": "algorand", "name": "algorand", "display_name": "Algorand Implementation", "description": "A comprehensive MCP server for tooling interactions (40+) and resource accessibility (60+) plus many useful prompts for interacting with the Algorand blockchain.", "repository": {"type": "git", "url": "https://github.com/GoPlausible/algorand-mcp"}, "homepage": "https://github.com/GoPlausible/algorand-mcp", "author": {"name": "GoPlausible", "email": "<EMAIL>", "url": "https://goplausible.com"}, "license": "MIT", "categories": ["Blockchain"], "tags": ["Algorand", "Blockchain", "Node.js"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@GoPlausible/algorand-mcp"], "env": {"NFD_API_KEY": "${NFD_API_KEY}", "NFD_API_URL": "${NFD_API_URL}", "ALGORAND_ALGOD": "${ALGORAND_ALGOD}", "ALGORAND_TOKEN": "${ALGORAND_TOKEN}", "ALGORAND_INDEXER": "${ALGORAND_INDEXER}", "ALGORAND_INDEXER_API": "${ALGORAND_INDEXER_API}", "ALGORAND_INDEXER_PORT": "${ALGORAND_INDEXER_PORT}", "ALGORAND_NETWORK": "${ALGORAND_NETWORK}"}}}, "arguments": {"NFD_API_KEY": {"description": "API key for the NFD service, required for accessing domain functionalities.", "required": true, "example": "your_nfd_api_key_here"}, "NFD_API_URL": {"description": "The URL endpoint for the NFD API service.", "required": false, "example": "https://api.nf.domains"}, "ALGORAND_ALGOD": {"description": "The URL endpoint for the Algorand Algod node.", "required": true, "example": "https://testnet-api.algonode.cloud"}, "ALGORAND_TOKEN": {"description": "The token required to interact with the Algorand Algod node, usually a blank string for testnets.", "required": false, "example": ""}, "ALGORAND_INDEXER": {"description": "The URL endpoint for the Algorand Indexer service.", "required": true, "example": "https://testnet-idx.algonode.cloud"}, "ALGORAND_INDEXER_API": {"description": "The API endpoint for accessing Algorand indexer functionalities.", "required": false, "example": "https://testnet-idx.algonode.cloud/v2"}, "ALGORAND_INDEXER_PORT": {"description": "The port for the Algorand indexer service, usually left blank for default settings.", "required": false, "example": ""}, "ALGORAND_NETWORK": {"description": "The network type being used (e.g., testnet or mainnet).", "required": true, "example": "testnet"}}}, {"id": "keycloak-mcp", "name": "keycloak-mcp", "display_name": "Keycloak Model Context Protocol", "description": "This MCP server enables natural language interaction with Keycloak for user and realm management including creating, deleting, and listing users and realms.", "repository": {"type": "git", "url": "https://github.com/ChristophEnglisch/keycloak-model-context-protocol"}, "homepage": "https://github.com/ChristophEnglisch/keycloak-model-context-protocol", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "categories": ["Management", "Administration"], "tags": ["Keycloak", "User Management", "Realm Management"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "keycloak-model-context-protocol"], "env": {"KEYCLOAK_URL": "${KEYCLOAK_URL}", "KEYCLOAK_ADMIN": "${KEYCLOAK_ADMIN}", "KEYCLOAK_ADMIN_PASSWORD": "${KEYCLOAK_ADMIN_PASSWORD}"}}}, "arguments": {"KEYCLOAK_URL": {"description": "The URL of the Keycloak server instance that the MCP will connect to.", "required": true, "example": "http://localhost:8080"}, "KEYCLOAK_ADMIN": {"description": "The admin username for accessing the Keycloak server.", "required": true, "example": "admin"}, "KEYCLOAK_ADMIN_PASSWORD": {"description": "The password for the admin user to access the Keycloak server.", "required": true, "example": "admin"}}}, {"id": "coin-api-mcp", "name": "coin-api-mcp", "display_name": "Coin API", "description": "Provides access to [coinmarketcap](https://coinmarketcap.com/) cryptocurrency data.", "repository": {"type": "git", "url": "https://github.com/longmans/coin_api_mcp"}, "homepage": "https://github.com/longmans/coin_api_mcp", "author": {"name": "longmans"}, "license": "MIT", "categories": ["Cryptocurrency", "API"], "tags": ["CoinMarketCap", "Cryptocurrency", "Data"], "installations": {"python": {"type": "python", "command": "python", "args": ["-m", "coin_api_mcp"], "env": {"COINMARKETCAP_API_KEY": "${COINMARKETCAP_API_KEY}"}}}, "examples": [{"title": "Fetch List of Coins", "description": "Retrieve a paginated list of all active cryptocurrencies with market data.", "prompt": "Call `listing-coins` to get the latest cryptocurrency listings."}, {"title": "Get Coin Information", "description": "Retrieve detailed information about a specific cryptocurrency by its ID or symbol.", "prompt": "Call `get-coin-info` using the cryptocurrency ID."}], "arguments": {"COINMARKETCAP_API_KEY": {"description": "The API key required to access CoinMarketCap data.", "required": true, "example": "your_api_key_here"}}}, {"id": "pif", "name": "pif", "display_name": "PIF Framework", "description": "A Personal Intelligence Framework (PIF), providing tools for file operations, structured reasoning, and journal-based documentation to support continuity and evolving human-AI collaboration across sessions.", "repository": {"type": "git", "url": "https://github.com/hungryrobot1/MCP-PIF"}, "homepage": "https://github.com/hungryrobot1/MCP-PIF", "author": {"name": "hungryrobot1"}, "license": "MIT", "categories": ["AI", "Development", "Framework"], "tags": ["PIF", "TypeScript", "Node.js"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/hungryrobot1/MCP-PIF"]}}, "examples": [{"title": "Reasoning Example", "description": "Create a structured thought pattern.", "prompt": "reason: { thoughts: [{ content: 'Initial observation' }, { content: 'Building on previous thought', relationType: 'sequence', relationTo: 0 }] }"}, {"title": "Journal Creation Example", "description": "Document development for future reference.", "prompt": "journal_create: { title: 'Implementation Pattern', content: 'Insights about development...', tags: ['development', 'patterns'] }"}], "arguments": {"MCP_WORKSPACE_ROOT": {"description": "Environment variable to specify a workspace location for the server.", "required": false, "example": "/path/to/workspace"}, "MCP_CONFIG": {"description": "Environment variable containing a JSON string of configuration options for the server.", "required": false, "example": "{\"key\": \"value\"}"}}}, {"id": "graphql-schema", "name": "graphql-schema", "display_name": "GraphQL Schema Model Context Protocol", "description": "Allow LLMs to explore large GraphQL schemas without bloating the context.", "repository": {"type": "git", "url": "https://github.com/hannesj/mcp-graphql-schema"}, "homepage": "https://github.com/hannesj/mcp-graphql-schema", "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "[NOT GIVEN]", "categories": ["GraphQL", "Server"], "tags": ["GraphQL", "LLMs", "<PERSON><PERSON><PERSON>", "API"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "mcp-graphql-schema", "/ABSOLUTE/PATH/TO/schema.graphqls"]}}, "examples": [{"title": "List all query fields", "description": "Retrieve a list of all available root-level fields for GraphQL queries.", "prompt": "What query fields are available in this GraphQL schema?"}, {"title": "User query field details", "description": "Get detailed definition for the \"user\" query field.", "prompt": "Show me the details of the \"user\" query field."}, {"title": "Mutation operations", "description": "List all mutation operations that can be performed in the schema.", "prompt": "What mutation operations can I perform in this schema?"}, {"title": "List all types", "description": "Retrieve a list of all types defined in the schema.", "prompt": "List all types defined in this schema."}, {"title": "Type definition", "description": "Show the definition of the \"Product\" type.", "prompt": "Show me the definition of the \"Product\" type."}, {"title": "Order type fields", "description": "List all fields of the \"Order\" type.", "prompt": "List all fields of the \"Order\" type."}, {"title": "Search for types and fields", "description": "Search the schema for types and fields related to \"customer.\"", "prompt": "Search for types and fields related to \"customer\"."}]}, {"id": "lightdash", "name": "lightdash", "display_name": "Lightdash", "description": "Interact with [Lightdash](https://www.lightdash.com/), a BI tool.", "repository": {"type": "git", "url": "https://github.com/syucream/lightdash-mcp-server"}, "homepage": "https://github.com/syucream/lightdash-mcp-server", "author": {"name": "syucream"}, "license": "MIT", "categories": ["Tools"], "tags": ["Lightdash", "AI"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "lightdash-mcp-server"], "env": {"LIGHTDASH_API_KEY": "${LIGHTDASH_API_KEY}", "LIGHTDASH_API_URL": "${LIGHTDASH_API_URL}"}}}, "arguments": {"LIGHTDASH_API_KEY": {"description": "Your Lightdash PAT (Personal Access Token) required for authenticating API requests.", "required": true, "example": "your_personal_access_token_here"}, "LIGHTDASH_API_URL": {"description": "The base URL for the Lightdash API that you are connecting to.", "required": true, "example": "https://your.base.url"}}}, {"id": "goodnews", "name": "goodnews", "display_name": "Goodnews", "description": "A simple MCP server that delivers curated positive and uplifting news stories.", "repository": {"type": "git", "url": "https://github.com/VectorInstitute/mcp-goodnews"}, "homepage": "https://github.com/VectorInstitute/mcp-goodnews", "author": {"name": "VectorInstitute"}, "license": "Apache 2.0", "categories": ["news", "sentiment"], "tags": ["positive news", "uplifting", "Cohere", "NewsAPI"], "examples": [{"title": "Fetch list of good news", "description": "Retrieve uplifting news articles using MCP Goodnews.", "prompt": "Show me some good news from today."}], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/VectorInstitute/mcp-goodnews", "server.py"], "env": {"NEWS_API_KEY": "<newsapi-api-key>", "COHERE_API_KEY": "<cohere-api-key>"}}}, "arguments": {"NEWS_API_KEY": {"description": "API key for NewsAPI to fetch news articles", "required": true, "example": "your_newsapi_key_here"}, "COHERE_API_KEY": {"description": "API key for Cohere to analyze sentiment of news articles", "required": true, "example": "your_cohere_api_key_here"}}}, {"id": "postman", "name": "postman", "display_name": "Postman", "description": "MCP server for running Postman Collections locally via Newman. Allows for simple execution of Postman Server and returns the results of whether the collection passed all the tests.", "repository": {"type": "git", "url": "https://github.com/shannon<PERSON>/mcp-postman"}, "homepage": "https://github.com/shannon<PERSON>/mcp-postman", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "license": "ISC", "categories": ["API Testing", "Automation"], "tags": ["Postman", "<PERSON>", "API"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/shannon<PERSON>/mcp-postman"]}}}, {"id": "reaper", "name": "reaper", "display_name": "Reaper", "description": "Interact with your [<PERSON>](https://www.reaper.fm/) (Digital Audio Workstation) projects.", "repository": {"type": "git", "url": "https://github.com/dschuler36/reaper-mcp-server"}, "homepage": "https://github.com/dschuler36/reaper-mcp-server", "author": {"name": "dschuler36"}, "license": "MIT", "categories": ["Audio"], "tags": ["Reaper", "<PERSON>"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "https://github.com/dschuler36/reaper-mcp-server", "reaper-mcp-server", "--reaper-projects-dir", "${REAPER_PROJECTS_DIR}"]}}, "examples": [{"title": "Ask about a Reaper project", "description": "Request information about a specific Reaper project you have.", "prompt": "What are the tracks in my 'Project A' Reaper file?"}, {"title": "Find Reaper projects", "description": "Use the tool to locate all Reaper projects in the configured directory.", "prompt": "Find all my Reaper projects."}], "arguments": {"REAPER_PROJECTS_DIR": {"description": "The directory where Reaper projects are stored, allowing the MCP server to find and interact with them.", "required": true, "example": "/path/to/reaper/projects"}}}, {"id": "hyperliquid", "name": "hyperliquid", "display_name": "Hyperliquid", "description": "An MCP server implementation that integrates the Hyperliquid SDK for exchange data.", "repository": {"type": "git", "url": "https://github.com/mektigboy/server-hyperliquid"}, "license": "MIT", "author": {"name": "mektigboy"}, "homepage": "https://github.com/mektigboy/server-hyperliquid", "categories": ["Exchange"], "tags": ["Hyperliquid", "Exchange"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@mektigboy/server-hyperliquid"]}}}, {"id": "evm-mcp-server", "name": "evm-mcp-server", "display_name": "EVM Server", "description": "Comprehensive blockchain services for 30+ EVM networks, supporting native tokens, ERC20, NFTs, smart contracts, transactions, and ENS resolution.", "repository": {"type": "git", "url": "https://github.com/mcpdotdirect/evm-mcp-server"}, "license": "MIT", "categories": ["Blockchain", "EVM", "Server"], "tags": ["Ethereum", "Smart Contracts", "AI", "Token Transfers", "NFTs"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@mcpdotdirect/evm-mcp-server"]}}, "author": {"name": "mcpdotdirect"}, "homepage": "https://github.com/mcpdotdirect/evm-mcp-server"}, {"id": "neovim", "name": "neovim", "display_name": "Neovim Server", "description": "An MCP Server for your Neovim session.", "repository": {"type": "git", "url": "https://github.com/bigcodegen/mcp-neovim-server"}, "homepage": "https://github.com/bigcodegen/mcp-neovim-server", "author": {"name": "bigcodegen"}, "license": "MIT", "categories": ["AI", "Editor"], "tags": ["<PERSON><PERSON><PERSON>", "MCP", "<PERSON>"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "mcp-neovim-server"], "env": {"ALLOW_SHELL_COMMANDS": "${ALLOW_SHELL_COMMANDS}", "NVIM_SOCKET_PATH": "${NVIM_SOCKET_PATH}"}}}, "arguments": {"ALLOW_SHELL_COMMANDS": {"description": "Set to 'true' to enable shell command execution (e.g. `!ls`).", "required": false, "example": "true"}, "NVIM_SOCKET_PATH": {"description": "Set to the path of your Neovi<PERSON> socket.", "required": false, "example": "/tmp/nvim"}}}, {"id": "aws-resources-operations", "name": "aws-resources-operations", "display_name": "AWS Resources", "description": "Run generated python code to securely query or modify any AWS resources supported by boto3.", "repository": {"type": "git", "url": "https://github.com/baryhuang/mcp-server-aws-resources-python"}, "homepage": "https://github.com/baryhuang/mcp-server-aws-resources-python", "author": {"name": "baryhuang"}, "license": "MIT", "categories": ["AWS", "<PERSON>er"], "tags": ["AWS", "<PERSON>er", "boto3"], "installations": {"docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "--rm", "-e", "AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}", "-e", "AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}", "-e", "AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION}", "buryhuang/mcp-server-aws-resources:latest"], "env": {"AWS_ACCESS_KEY_ID": "${AWS_ACCESS_KEY_ID}", "AWS_SECRET_ACCESS_KEY": "${AWS_SECRET_ACCESS_KEY}", "AWS_DEFAULT_REGION": "${AWS_DEFAULT_REGION}"}}}, "arguments": {"AWS_ACCESS_KEY_ID": {"description": "Your AWS access key.", "required": true, "example": "your_access_key_id_here"}, "AWS_SECRET_ACCESS_KEY": {"description": "Your AWS secret key.", "required": true, "example": "your_secret_access_key_here"}, "AWS_DEFAULT_REGION": {"description": "AWS region to operate in. Defaults to 'us-east-1' if not set.", "required": false, "example": "us-east-1"}}}, {"id": "filesystem", "name": "filesystem", "display_name": "Filesystem", "description": "Secure file operations with configurable access controls", "repository": {"type": "git", "url": "https://github.com/modelcontextprotocol/servers"}, "homepage": "https://github.com/modelcontextprotocol/servers/blob/main/src/filesystem", "author": {"name": "modelcontextprotocol"}, "license": "MIT", "categories": ["filesystem"], "tags": ["Node.js", "server", "filesystem", "operations"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Desktop", "/path/to/other/allowed/dir"]}, "docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "--rm", "--mount", "type=bind,src=/Users/<USER>/Desktop,dst=/projects/Desktop", "--mount", "type=bind,src=/path/to/other/allowed/dir,dst=/projects/other/allowed/dir,ro", "--mount", "type=bind,src=/path/to/file.txt,dst=/projects/path/to/file.txt", "mcp/filesystem", "/projects"]}}}, {"id": "ergo-blockchain-mcp", "name": "ergo-blockchain-mcp", "display_name": "Ergo Blockchain Explorer", "description": "-An MCP server to integrate Ergo Blockchain Node and Explorer APIs for checking address balances, analyzing transactions, viewing transaction history, performing forensic analysis of addresses, searching for tokens, and monitoring network status.", "repository": {"type": "git", "url": "https://github.com/marctheshark3/ergo-mcp"}, "homepage": "https://github.com/marctheshark3/ergo-mcp", "author": {"name": "marctheshark3"}, "license": "MIT", "categories": ["Blockchain", "Server"], "tags": ["Ergo", "Blockchain", "Python", "API"], "examples": [{"title": "Running the MCP Server as a Module", "description": "Run the server using Python module command.", "prompt": "```bash\n# Make sure your virtual environment is activated:\n# Using the full path (recommended):\n/path/to/your/project/.venv/bin/python -m ergo_explorer\n\n# Or with activated virtual environment:\npython -m ergo_explorer\n```"}, {"title": "Running Tests", "description": "Execute tests using pytest framework.", "prompt": "```bash\n# Run all tests\npython -m pytest\n\n# Run specific test files\npython -m pytest tests/unit/test_address_tools.py\n```"}], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/marctheshark3/ergo-mcp", "ergo_explorer"], "env": {"SERVER_HOST": "<YOUR_HOST>", "SERVER_PORT": "<YOUR_PORT>", "SERVER_WORKERS": "<YOUR_WORKERS>", "ERGO_NODE_API": "<YOUR_ERGO_NODE_API>", "ERGO_NODE_API_KEY": "<YOUR_ERGO_NODE_API_KEY>"}}}, "arguments": {"SERVER_HOST": {"description": "Host to bind the server to (default: 0.0.0.0)", "required": false, "example": "localhost"}, "SERVER_PORT": {"description": "Port to run the server on (default: 3001)", "required": false, "example": "3001"}, "SERVER_WORKERS": {"description": "Number of worker processes (default: 4)", "required": false, "example": "4"}, "ERGO_NODE_API": {"description": "URL of the Ergo node API (for node-specific features)", "required": false, "example": "http://localhost:8080"}, "ERGO_NODE_API_KEY": {"description": "API key for the Ergo node (if required)", "required": false, "example": "your_api_key"}}}, {"id": "nasa", "name": "nasa", "display_name": "NASA", "description": "Access to a unified gateway of NASA's data sources including but not limited to APOD, NEO, EPIC, GIBS.", "repository": {"type": "git", "url": "https://github.com/ProgramComputer/NASA-MCP-server"}, "homepage": "https://github.com/ProgramComputer/NASA-MCP-server", "author": {"name": "ProgramComputer"}, "license": "ISC", "categories": ["NASA", "API", "Data", "Server"], "tags": ["NASA", "API", "Data", "Space", "Science"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@programcomputer/nasa-mcp-server"], "env": {"NASA_API_KEY": "${NASA_API_KEY}"}}}, "examples": [{"title": "Get Today's Astronomy Picture of the Day", "description": "Fetch the APOD from NASA's API.", "prompt": "GET /nasa/apod"}, {"title": "Get Mars Rover Photos", "description": "Retrieve photos taken by the Curiosity rover on a specific sol.", "prompt": "GET /nasa/mars-rover?rover=curiosity&sol=1000"}, {"title": "Search for Near Earth Objects", "description": "Find any near earth objects recorded in a specified date range.", "prompt": "GET /nasa/neo?start_date=2023-01-01&end_date=2023-01-07"}], "arguments": {"NASA_API_KEY": {"description": "Your NASA API key (get at api.nasa.gov)", "required": false, "example": "DEMO_KEY"}}}, {"id": "discourse", "name": "discourse", "display_name": "Discourse", "description": "A MCP server to search Discourse posts on a Discourse forum.", "repository": {"type": "git", "url": "https://github.com/AshDevFr/discourse-mcp-server"}, "license": "MIT", "tags": ["discourse", "search"], "author": {"name": "AshDevFr"}, "homepage": "https://github.com/AshDevFr/discourse-mcp-server", "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@ashdev/discourse-mcp-server"], "env": {"DISCOURSE_API_URL": "${DISCOURSE_API_URL}", "DISCOURSE_API_KEY": "${DISCOURSE_API_KEY}", "DISCOURSE_API_USERNAME": "${DISCOURSE_API_USERNAME}"}}, "docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "--rm", "-e", "DISCOURSE_API_URL=${DISCOURSE_API_URL}", "-e", "DISCOURSE_API_KEY=${DISCOURSE_API_KEY}", "-e", "DISCOURSE_API_USERNAME=${DISCOURSE_API_USERNAME}", "ashdev/discourse-mcp-server"]}}, "arguments": {"DISCOURSE_API_URL": {"description": "API URL for the Discourse forum that the server will connect to.", "required": true, "example": "https://try.discourse.org"}, "DISCOURSE_API_KEY": {"description": "API key for authenticating to the Discourse forum.", "required": true, "example": "1234"}, "DISCOURSE_API_USERNAME": {"description": "Username for authenticating to the Discourse forum.", "required": true, "example": "ash"}}}, {"id": "webflow", "name": "webflow", "display_name": "Webflow", "description": "Interfact with the Webflow APIs", "repository": {"type": "git", "url": "https://github.com/kapilduraphe/webflow-mcp-server"}, "homepage": "https://github.com/kapilduraphe/webflow-mcp-server", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["web"], "tags": ["webflow", "api"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/kapilduraphe/webflow-mcp-server"], "env": {"WEBFLOW_API_TOKEN": "${WEBFLOW_API_TOKEN}"}}}, "examples": [{"title": "Get Sites", "description": "Retrieve a list of all Webflow sites accessible to the authenticated user.", "prompt": "get_sites"}, {"title": "Get Site", "description": "Retrieve detailed information about a specific Webflow site by ID.", "prompt": "get_site siteId"}], "arguments": {"WEBFLOW_API_TOKEN": {"description": "Your Webflow API token to authenticate requests to the Webflow API. This token is required for the server to function and should be kept secure.", "required": true, "example": "your-api-token"}}}, {"id": "airtable", "name": "airtable", "display_name": "Airtable", "description": "Airtable Model Context Protocol Server.", "repository": {"type": "git", "url": "https://github.com/felores/airtable-mcp"}, "author": {"name": "felores"}, "license": "MIT", "categories": ["API", "Data Management"], "tags": ["Airtable", "Database", "API"], "arguments": {"AIRTABLE_API_KEY": {"description": "Airtable API key for authenticating with the Airtable API", "required": true, "example": "pat.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"}}, "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@felores/airtable-mcp-server"], "env": {"AIRTABLE_API_KEY": "${AIRTABLE_API_KEY}"}, "description": "Run with npx (requires npm install)"}}, "homepage": "https://github.com/felores/airtable-mcp"}, {"id": "sequential-thinking", "name": "sequential-thinking", "display_name": "Sequential Thinking", "description": "Dynamic and reflective problem-solving through thought sequences", "repository": {"type": "git", "url": "https://github.com/modelcontextprotocol/servers"}, "homepage": "https://github.com/modelcontextprotocol/servers/blob/main/src/sequentialthinking", "author": {"name": "modelcontextprotocol"}, "license": "MIT", "categories": ["Problem Solving", "Tools"], "tags": ["dynamic thinking", "reflective process", "structured thinking"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "docker": {"type": "docker", "command": "docker", "args": ["run", "--rm", "-i", "mcp/sequentialthinking"]}}, "examples": [{"title": "Example Usage", "description": "Using the Sequential Thinking tool for a complex problem", "prompt": "Break down the complex problem of organizing an event into manageable steps."}]}, {"id": "hdw-linkedin", "name": "hdw-linkedin", "display_name": "HDW", "description": "Access to profile data and management of user account with [HorizonDataWave.ai](https://horizondatawave.ai/).", "repository": {"type": "git", "url": "https://github.com/horizondatawave/hdw-mcp-server"}, "homepage": "https://github.com/horizondatawave/hdw-mcp-server", "author": {"name": "horizondatawave"}, "license": "MIT", "categories": ["API", "LinkedIn", "Data Management"], "tags": ["LinkedIn", "API access", "Data retrieval", "User management"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["@horizondatawave/mcp"], "env": {"HDW_ACCESS_TOKEN": "${HDW_ACCESS_TOKEN}", "HDW_ACCOUNT_ID": "${HDW_ACCOUNT_ID}"}}}, "arguments": {"HDW_ACCESS_TOKEN": {"description": "Access token for HorizonDataWave API, used for authentication and authorization to access user data.", "required": true, "example": "YOUR_HD_W_ACCESS_TOKEN"}, "HDW_ACCOUNT_ID": {"description": "Account ID for HorizonDataWave API, used to identify the user's account.", "required": true, "example": "YOUR_HD_W_ACCOUNT_ID"}}}, {"id": "unity-integration-advanced", "name": "unity-integration-advanced", "display_name": "Unity Integration", "description": "Advanced Unity3d Game Engine MCP which supports ,Execution of Any Editor Related Code Directly Inside of Unity, Fetch Logs, Get Editor State and Allow File Access of the Project making it much more useful in Script Editing or asset creation.", "repository": {"type": "git", "url": "https://github.com/quazaai/UnityMCPIntegration"}, "homepage": "https://github.com/quazaai/UnityMCPIntegration", "author": {"name": "qua<PERSON><PERSON>"}, "license": "MIT", "categories": ["Unity", "AI"], "tags": ["Unity", "Integration", "AI"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/quazaai/UnityMCPIntegration"], "env": {"MCP_WEBSOCKET_PORT": "${MCP_WEBSOCKET_PORT}"}}}, "examples": [{"title": "Get Unity Editor State", "description": "Retrieve comprehensive information about the current Unity project and editor state.", "prompt": "get_editor_state()"}, {"title": "Execute C# Code", "description": "Run specific C# code directly within the Unity Editor.", "prompt": "execute_editor_command('Debug.Log(\"Hello, World!\");')"}], "arguments": {"MCP_WEBSOCKET_PORT": {"description": "Environment variable to specify the WebSocket port used by the MCP server.", "required": false, "example": "5010"}}}, {"id": "playwright", "name": "playwright", "display_name": "Playwright", "description": "This MCP Server will help you run browser automation and webscraping using Playwright", "repository": {"type": "git", "url": "https://github.com/executeautomation/mcp-playwright"}, "homepage": "https://github.com/executeautomation/mcp-playwright", "author": {"name": "executeautomation"}, "license": "MIT", "categories": ["Automation", "Web Testing"], "tags": ["Playwright", "Browser Automation"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"]}}}, {"id": "productboard", "name": "productboard", "display_name": "Productboard", "description": "Integrate the Productboard API into agentic workflows via MCP.", "repository": {"type": "git", "url": "https://github.com/kenjihikmatullah/productboard-mcp"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["API", "Integration"], "tags": ["Productboard", "API"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "productboard-mcp"], "env": {"PRODUCTBOARD_ACCESS_TOKEN": "<YOUR_TOKEN>"}}}, "homepage": "https://github.com/kenjihikmatullah/productboard-mcp", "arguments": {"PRODUCTBOARD_ACCESS_TOKEN": {"description": "An access token needed to authenticate with the Productboard API. This token is required to make requests to the API and must be kept confidential.", "required": true, "example": "your_access_token_here"}}}, {"id": "qwen-max", "name": "qwen-max", "display_name": "<PERSON><PERSON>", "description": "A Model Context Protocol (MCP) server implementation for the Qwen models.", "repository": {"type": "git", "url": "https://github.com/66julienmartin/MCP-server-<PERSON><PERSON>_<PERSON>"}, "homepage": "https://github.com/66julienmartin/MCP-server-<PERSON><PERSON>_<PERSON>", "author": {"name": "66<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["AI"], "tags": ["<PERSON><PERSON>", "Server"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@gongrzhe/quickchart-mcp-server"]}}, "arguments": {"DASHSCOPE_API_KEY": {"description": "API key required for authentication with the Dashscope service.", "required": true, "example": "your-api-key-here"}}}, {"id": "fetch", "name": "fetch", "display_name": "<PERSON>tch", "description": "A server that flexibly fetches HTML, JSON, Markdown, or plaintext.", "repository": {"type": "git", "url": "https://github.com/zcaceres/fetch-mcp"}, "homepage": "https://github.com/zcaceres/fetch-mcp", "author": {"name": "zcaceres"}, "license": "MIT", "categories": ["Web", "Content Fetching"], "tags": ["fetch", "web", "api", "html", "json", "markdown", "plain text"], "examples": [{"title": "Fetch HTML", "description": "Fetch a website and return the content as HTML", "prompt": "fetch_html(url: string, headers?: object) -> string"}, {"title": "Fetch JSON", "description": "Fetch a JSON file from a URL", "prompt": "fetch_json(url: string, headers?: object) -> object"}, {"title": "Fetch Plain Text", "description": "Fetch a website and return the content as plain text", "prompt": "fetch_txt(url: string, headers?: object) -> string"}, {"title": "<PERSON><PERSON>", "description": "Fetch a website and return the content as Markdown", "prompt": "fetch_markdown(url: string, headers?: object) -> string"}], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/zcaceres/fetch-mcp"]}}, "arguments": {"url": {"description": "URL of the website to fetch", "required": true, "example": "https://example.com"}, "headers": {"description": "Custom headers to include in the request", "required": false, "example": "{\"Authorization\": \"Bearer token\"}"}}}, {"id": "inoyu", "name": "inoyu", "display_name": "Inoyu Apache Unomi", "description": "Interact with an Apache Unomi CDP customer data platform to retrieve and update customer profiles", "repository": {"type": "git", "url": "https://github.com/sergehuber/inoyu-mcp-unomi-server"}, "homepage": "https://github.com/sergehuber/inoyu-mcp-unomi-server", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["User Context Management"], "tags": ["Apache Unomi", "User Profiles", "Context Management"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["@inoyu/mcp-unomi-server"], "env": {"UNOMI_BASE_URL": "${UNOMI_BASE_URL}", "UNOMI_USERNAME": "${UNOMI_USERNAME}", "UNOMI_PASSWORD": "${UNOMI_PASSWORD}", "UNOMI_PROFILE_ID": "${UNOMI_PROFILE_ID}", "UNOMI_KEY": "${UNOMI_KEY}", "UNOMI_EMAIL": "${UNOMI_EMAIL}", "UNOMI_SOURCE_ID": "${UNOMI_SOURCE_ID}"}}}, "arguments": {"UNOMI_BASE_URL": {"description": "The base URL of your Apache Unomi server (e.g., http://your-unomi-server:8181)", "required": true}, "UNOMI_USERNAME": {"description": "The username to authenticate with the Apache Unomi server, default is 'karaf'", "required": true}, "UNOMI_PASSWORD": {"description": "The password to authenticate with the Apache Unomi server, default is 'karaf'", "required": true}, "UNOMI_PROFILE_ID": {"description": "The ID of the user profile to be used for context management", "required": false}, "UNOMI_KEY": {"description": "The authorization key required for secured operations with the Unomi server, defaults to '670c26d1cc413346c3b2fd9ce65dab41'", "required": false}, "UNOMI_EMAIL": {"description": "The email address associated with the user profile, used for profile lookup", "required": false}, "UNOMI_SOURCE_ID": {"description": "An identifier for the source of the request (e.g., claude-desktop)", "required": false}}}, {"id": "everything", "name": "everything", "display_name": "Everything", "description": "This MCP server exercises all the features of the MCP protocol. It is a test server for builders of MCP clients.", "repository": {"type": "git", "url": "https://github.com/modelcontextprotocol/servers"}, "homepage": "https://github.com/modelcontextprotocol/servers/tree/main/src/everything#readme", "author": {"name": "MCP Team"}, "license": "MIT", "categories": ["development", "testing"], "tags": ["testing", "reference", "example", "demo"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-everything"], "package": "@modelcontextprotocol/server-everything", "env": {}, "description": "Install and run using NPX", "recommended": true}}, "examples": [{"title": "Test tool usage", "description": "Test various tools provided by the server", "prompt": "Show me how to use the different tools in this MCP server."}, {"title": "Test resources", "description": "Demonstrate accessing resources", "prompt": "Demonstrate how to access and use resources from this MCP server."}]}, {"id": "godot", "name": "godot", "display_name": "<PERSON><PERSON>", "description": "A MCP server providing comprehensive Godot engine integration for project editing, debugging, and scene management.", "repository": {"type": "git", "url": "https://github.com/Coding-Solo/godot-mcp"}, "homepage": "https://github.com/Coding-Solo/godot-mcp", "author": {"name": "Coding Solo", "url": "https://github.com/Coding-Solo"}, "license": "MIT", "categories": ["Game Development"], "tags": ["<PERSON><PERSON>", "AI", "Game"], "examples": [{"title": "Launch Godot Editor", "description": "Launch the Godot editor for a specific project.", "prompt": "Launch the Godot editor for my project at /path/to/project"}, {"title": "Run Godot Project", "description": "Execute Godot projects in debug mode.", "prompt": "Run my Godot project and show me any errors"}, {"title": "Get Project Info", "description": "Retrieve detailed information about the project structure.", "prompt": "Get information about my Godot project structure"}, {"title": "Debug Assistance", "description": "Help debug errors in Godot projects.", "prompt": "Help me debug this error in my Godot project: [paste error]"}], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/Coding-Solo/godot-mcp"]}}}, {"id": "aws", "name": "aws", "display_name": "AWS", "description": "Perform operations on your AWS resources using an LLM.", "repository": {"type": "git", "url": "https://github.com/rishikavikondala/mcp-server-aws"}, "homepage": "https://github.com/rishikavikondala/mcp-server-aws", "author": {"name": "rishikavikondala"}, "license": "MIT", "categories": ["AWS", "Server"], "tags": ["s3", "dynamodb", "aws"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--directory", "/path/to/repo/mcp-server-aws", "mcp-server-aws"]}}, "arguments": {"AWS_ACCESS_KEY_ID": {"description": "This is the access key ID for your AWS account, required for authenticating requests to AWS services.", "required": true, "example": "AKIAEXAMPLE"}, "AWS_SECRET_ACCESS_KEY": {"description": "This is the secret access key for your AWS account, used in conjunction with the access key ID to authenticate requests.", "required": true, "example": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"}, "AWS_REGION": {"description": "This specifies the AWS region you want to use for your operations. It defaults to `us-east-1` if not provided.", "required": false, "example": "us-west-2"}}}, {"id": "github-actions", "name": "github-actions", "display_name": "GitHub Actions", "description": "A Model Context Protocol (MCP) server for interacting with Github Actions.", "repository": {"type": "git", "url": "https://github.com/ko1ynnky/github-actions-mcp-server"}, "homepage": "https://github.com/ko1ynnky/github-actions-mcp-server", "author": {"name": "ko1ynnky"}, "license": "MIT", "categories": ["GitHub", "API"], "tags": ["GitHub Actions", "Workflow Management", "Automation"], "examples": [{"title": "List Workflows", "description": "List workflows in a GitHub repository.", "prompt": "const result = await listWorkflows({ owner: 'your-username', repo: 'your-repository' });"}, {"title": "Trigger Workflow", "description": "Trigger a workflow in a GitHub repository.", "prompt": "const result = await triggerWorkflow({ owner: 'your-username', repo: 'your-repository', workflowId: 'ci.yml', ref: 'main', inputs: { environment: 'production' }});"}], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/ko1ynnky/github-actions-mcp-server"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_PERSONAL_ACCESS_TOKEN}"}}}, "arguments": {"GITHUB_PERSONAL_ACCESS_TOKEN": {"description": "A personal access token required for authentication with GitHub API, used to access user repositories and perform actions.", "required": true, "example": "ghp_16CharTokenHere"}}}, {"id": "docker", "name": "docker", "display_name": "Docker Integration", "description": "Integrate with <PERSON><PERSON> to manage containers, images, volumes, and networks.", "repository": {"type": "git", "url": "https://github.com/ckreiling/mcp-server-docker"}, "license": "MIT", "examples": [{"title": "Deploy an nginx container", "description": "Deploy an nginx container exposing it on port 9000", "prompt": "name: `nginx`, containers: \"deploy an nginx container exposing it on port 9000\""}, {"title": "Deploy a WordPress and MySQL container", "description": "Deploy a WordPress container and a supporting MySQL container, exposing WordPress on port 9000", "prompt": "name: `wordpress`, containers: \"deploy a WordPress container and a supporting MySQL container, exposing Wordpress on port 9000\""}], "categories": ["<PERSON>er"], "tags": ["<PERSON>er", "Container", "Image", "Volume", "Network"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/ckreiling/mcp-server-docker", "mcp-server-docker"]}}}, {"id": "openrpc", "name": "openrpc", "display_name": "OpenRPC", "description": "Interact with and discover JSON-RPC APIs via [OpenRPC](https://open-rpc.org/).", "repository": {"type": "git", "url": "https://github.com/shanejonas/openrpc-mpc-server"}, "homepage": "https://github.com/shanejonas/openrpc-mpc-server", "author": {"name": "s<PERSON><PERSON><PERSON>"}, "license": "MIT", "categories": ["JSON-RPC"], "tags": ["OpenRPC", "JSON-RPC"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "openrpc-mpc-server"]}}}, {"id": "xero-mcp-server", "name": "xero-mcp-server", "display_name": "Xero", "description": "Enabling clients to interact with Xero system for streamlined accounting, invoicing, and business operations.", "repository": {"type": "git", "url": "https://github.com/john-zhang-dev/xero-mcp"}, "license": "MIT", "examples": [{"title": "Visualize my financial position over the last month", "description": "", "prompt": "Visualize my financial position over the last month"}, {"title": "Track my spendings over last week", "description": "", "prompt": "Track my spendings over last week"}, {"title": "Add all transactions from the monthly statement into my revenue account (account code 201) as receive money", "description": "", "prompt": "Add all transactions from the monthly statement into my revenue account (account code 201) as receive money"}], "author": {"name": "john-zhang-dev"}, "homepage": "https://github.com/john-zhang-dev/xero-mcp", "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "xero-mcp@latest"], "env": {"XERO_CLIENT_ID": "${XERO_CLIENT_ID}", "XERO_CLIENT_SECRET": "${XERO_CLIENT_SECRET}", "XERO_REDIRECT_URI": "${XERO_REDIRECT_URI}"}}}, "arguments": {"XERO_CLIENT_ID": {"description": "The Client ID obtained from the Xero Developer center after creating an OAuth 2.0 app, required for authentication.", "required": true, "example": "YOUR_CLIENT_ID"}, "XERO_CLIENT_SECRET": {"description": "The Client Secret generated in the Xero Developer center, necessary for authenticating requests.", "required": true, "example": "YOUR_CLIENT_SECRET"}, "XERO_REDIRECT_URI": {"description": "The URI to redirect to after authentication, should typically match the redirect URI specified in the OAuth 2.0 app settings.", "required": false, "example": "http://localhost:5000/callback"}}}, {"id": "home-assistant", "name": "home-assistant", "display_name": "<PERSON><PERSON>", "description": "Docker-ready MCP server for Home Assistant with entity management, domain summaries, automation support, and guided conversations. Includes pre-built container images for easy installation.", "repository": {"type": "git", "url": "https://github.com/voska/hass-mcp"}, "homepage": "https://github.com/voska/hass-mcp", "author": {"name": "voska"}, "license": "MIT", "categories": ["Home Automation", "AI Integration"], "tags": ["Home Assistant", "<PERSON>", "LLM", "Automation"], "installations": {"docker": {"type": "docker", "command": "docker", "args": ["run", "-i", "--rm", "-e", "HA_URL", "-e", "HA_TOKEN", "voska/hass-mcp"], "env": {"HA_URL": "http://homeassistant.local:8123", "HA_TOKEN": "YOUR_LONG_LIVED_TOKEN"}}}, "examples": [{"title": "Get Current State", "description": "Retrieve the current state of a specific device.", "prompt": "What's the current state of my living room lights?"}, {"title": "Turn Off Lights", "description": "Command to turn off lights in a specific area.", "prompt": "Turn off all the lights in the kitchen"}, {"title": "List Temperature Sensors", "description": "List all sensors related to temperature readings.", "prompt": "List all my sensors that contain temperature data"}, {"title": "Climate Summary", "description": "Get a summary of climate-related entities.", "prompt": "Give me a summary of my climate entities"}, {"title": "Create Automation", "description": "Create an automation based on a specific condition.", "prompt": "Create an automation that turns on the lights at sunset"}, {"title": "Troubleshoot Automation", "description": "Help troubleshoot an automation issue.", "prompt": "Help me troubleshoot why my bedroom motion sensor automation isn't working"}, {"title": "Search Entities", "description": "Search for specific entities related to a query.", "prompt": "Search for entities related to my living room"}], "arguments": {"HA_URL": {"description": "The URL for the Home Assistant instance where the Hass-MCP server will connect to retrieve and manage entities.", "required": true, "example": "http://homeassistant.local:8123"}, "HA_TOKEN": {"description": "The Long-Lived Access Token from Home Assistant, required for authentication to access the Home Assistant API.", "required": true, "example": "YOUR_LONG_LIVED_TOKEN"}}}, {"id": "ns-travel-information", "name": "ns-travel-information", "display_name": "NS Travel Information", "description": "Access Dutch Railways (NS) real-time train travel information and disruptions through the official NS API.", "repository": {"type": "git", "url": "https://github.com/r-huijts/ns-mcp-server"}, "homepage": "https://github.com/r-huijts/ns-mcp-server", "author": {"name": "r-huijts"}, "license": "MIT", "categories": ["Transport", "Travel Information"], "tags": ["NS", "Train", "Travel", "Information"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "ns-mcp-server"], "env": {"NS_API_KEY": "${NS_API_KEY}"}}}, "examples": [{"title": "Check train status", "description": "Ask if the 8:15 train from Almere to Amsterdam is running on time.", "prompt": "Is my usual 8:15 train from Almere to Amsterdam running on time?"}, {"title": "Inquire about delays", "description": "Find out if there are any delays on a specific route.", "prompt": "Are there any delays on the Rotterdam-Den Haag route today?"}, {"title": "Alternative routes", "description": "Seek alternative routes in case of maintenance on the direct line.", "prompt": "What's the best alternative route to Utrecht if there's maintenance on the direct line?"}, {"title": "Get ticket price", "description": "Ask for ticket prices for travel between cities.", "prompt": "How much does a first-class ticket from Amsterdam to Rotterdam cost?"}], "arguments": {"NS_API_KEY": {"description": "Your NS API key, required for authenticating API requests to access NS travel information.", "required": true, "example": "your_api_key_here"}}}, {"id": "unity-catalog", "name": "unity-catalog", "display_name": "Unity Catalog", "description": "An MCP server that enables LLMs to interact with Unity Catalog AI, supporting CRUD operations on Unity Catalog Functions and executing them as MCP tools.", "repository": {"type": "git", "url": "https://github.com/ognis1205/mcp-server-unitycatalog"}, "homepage": "https://github.com/ognis1205/mcp-server-unitycatalog", "author": {"name": "ognis1205"}, "license": "MIT", "categories": ["Database"], "tags": ["Unity Catalog", "API", "Functions"], "installations": {"uvx": {"type": "uvx", "command": "uvx", "args": ["--from", "git+https://github.com/ognis1205/mcp-server-unitycatalog", "mcp-server-unitycatalog", "--uc_server", "${UC_SERVER}", "--uc_catalog", "${UC_CATALOG}", "--uc_schema", "${UC_SCHEMA}"]}, "docker": {"type": "docker", "command": "docker", "args": ["run", "--rm", "-i", "mcp/unitycatalog", "--uc_server", "${UC_SERVER}", "--uc_catalog", "${UC_CATALOG}", "--uc_schema", "${UC_SCHEMA}"]}}, "arguments": {"UC_SERVER": {"description": "The base URL of the Unity Catalog server.", "required": true, "example": "https://my-unity-catalog.com"}, "UC_CATALOG": {"description": "The name of the Unity Catalog catalog.", "required": true, "example": "my_catalog"}, "UC_SCHEMA": {"description": "The name of the schema within a Unity Catalog catalog.", "required": true, "example": "my_schema"}}}, {"id": "typesense", "name": "typesense", "display_name": "Typesense", "description": "A Model Context Protocol (MCP) server implementation that provides AI models with access to Typesense search capabilities. This server enables LLMs to discover, search, and analyze data stored in Typesense collections.", "repository": {"type": "git", "url": "https://github.com/suhail-ak-s/mcp-typesense-server"}, "homepage": "https://github.com/suhail-ak-s/mcp-typesense-server", "author": {"name": "suhail-ak-s"}, "license": "MIT", "categories": ["Search", "AI"], "tags": ["Typesense", "Server", "Search"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "typesense-mcp-server", "--host", "${TYPESENSE_HOST}", "--port", "8108", "--protocol", "http", "--api-key", "${API_KEY}"]}}, "examples": [{"title": "Example Usage with <PERSON>", "description": "Configuration for using Typesense MCP Server with Claude Desktop.", "prompt": "{\"mcpServers\": {\"typesense\": {\"command\": \"npx\",\"args\": [\"-y\",\"typesense-mcp-server\",\"--host\", \"your-typesense-host\",\"--port\", \"8108\",\"--protocol\", \"http\",\"--api-key\", \"your-api-key\"]}}}"}], "arguments": {"TYPESENSE_HOST": {"description": "The host for the Typesense server. This is the address where your Typesense server is running.", "required": true, "example": "localhost"}, "API_KEY": {"description": "The API key for accessing the Typesense server. This is needed for authentication when making requests to the server.", "required": true, "example": "your_api_key_here"}}}, {"id": "chatsum", "name": "chatsum", "display_name": "<PERSON><PERSON>", "description": "Query and Summarize chat messages with LLM. by [mcpso](https://mcp.so/)", "repository": {"type": "git", "url": "https://github.com/mcpso/mcp-server-chatsum"}, "homepage": "https://github.com/mcpso/mcp-server-chatsum", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://bento.me/idoubi"}, "license": "MIT", "categories": ["chat", "summarization"], "tags": ["chat", "summary"], "examples": [{"title": "Summarize Chat Messages", "description": "Use this prompt to summarize chat messages based on given parameters.", "prompt": "Summarize these messages: [...]"}], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/mcpso/mcp-server-chatsum"], "env": {"CHAT_DB_PATH": "path-to/mcp-server-chatsum/chatbot/data/chat.db"}}}, "arguments": {"CHAT_DB_PATH": {"description": "Path to your chat database file that the server will use to store and retrieve chat messages.", "required": true, "example": "path-to/mcp-server-chatsum/chatbot/data/chat.db"}}}, {"id": "descope", "name": "descope", "display_name": "Descope", "description": "An MCP server to integrate with [Descope](https://descope.com/) to search audit logs, manage users, and more.", "repository": {"type": "git", "url": "https://github.com/descope-sample-apps/descope-mcp-server"}, "homepage": "https://github.com/descope-sample-apps/descope-mcp-server", "author": {"name": "Descope", "url": "https://descope.com"}, "license": "MIT", "categories": ["Descope", "Management"], "tags": ["Descope", "API", "Server"], "installations": {"npm": {"type": "npm", "command": "npx", "args": ["-y", "https://github.com/descope-sample-apps/descope-mcp-server"], "env": {"DESCOPE_PROJECT_ID": "your-descope-project-id-here", "DESCOPE_MANAGEMENT_KEY": "your-descope-management-key-here"}}}, "arguments": {"DESCOPE_PROJECT_ID": {"description": "Your Descope Project ID", "required": true, "example": "12345-abcde-67890-fghij"}, "DESCOPE_MANAGEMENT_KEY": {"description": "Your Descope Management Key", "required": true, "example": "sk_test_4eC39HqLyjEDERyCzKZQz9fgo"}}}]