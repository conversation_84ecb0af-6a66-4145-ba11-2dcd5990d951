# 贡献指南

感谢您对 MCP Advisor 项目的关注！我们欢迎各种形式的贡献，包括但不限于功能开发、错误修复、文档改进和测试用例编写。

## 贡献流程

1. **Fork 仓库**：在 GitHub 上 fork 本仓库到您的账户
2. **克隆仓库**：`git clone https://github.com/YOUR-USERNAME/mcpadvisor.git`
3. **创建分支**：`git checkout -b feature/your-feature-name`
4. **进行更改**：根据[最佳实践指南](docs/BEST_PRACTICES.md)进行代码更改
5. **提交更改**：`git commit -m "feat: 添加新功能"`（请遵循[提交消息规范](docs/BEST_PRACTICES.md#提交消息规范)）
6. **推送更改**：`git push origin feature/your-feature-name`
7. **创建 Pull Request**：在 GitHub 上创建 PR，详细描述您的更改

## 提交 PR 前的检查清单

在提交 Pull Request 之前，请确保：

- [ ] 您的代码遵循了[最佳实践指南](docs/BEST_PRACTICES.md)中的编码标准
- [ ] 所有测试都通过了
- [ ] 您已添加必要的文档
- [ ] 您的提交消息遵循了[提交消息规范](docs/BEST_PRACTICES.md#提交消息规范)
- [ ] 您的代码已经过 lint 检查
- [ ] 您已更新相关的文档（如有必要）

## 代码审查流程

所有提交的 PR 都将经过代码审查。审查者可能会提出问题或建议，请及时回应。一旦 PR 被批准，它将被合并到主分支。

## 开发环境设置

请参考[开发者指南](docs/DEVELOPER_GUIDE.md)设置您的开发环境。

## 编码标准和最佳实践

**重要提示**：在开始编码之前，请务必阅读我们的[最佳实践指南](docs/BEST_PRACTICES.md)。该文档详细介绍了项目的编码标准、TypeScript 使用规范、函数式编程原则、测试方法和错误处理等方面的最佳实践。

遵循这些最佳实践将帮助保持代码库的一致性和可维护性，同时提高您的 PR 被接受的可能性。

## 报告问题

如果您发现了 bug 或有新功能建议，请在 GitHub 上创建一个 issue。请尽可能详细地描述问题或建议，包括：

- 对于 bug：复现步骤、预期行为和实际行为
- 对于功能请求：详细描述功能及其用例

## 许可证

通过贡献代码，您同意您的贡献将在项目的 MIT 许可证下发布。

感谢您的贡献！
