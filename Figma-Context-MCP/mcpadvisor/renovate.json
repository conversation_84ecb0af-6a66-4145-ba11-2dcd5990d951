{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:base"], "packageRules": [{"matchUpdateTypes": ["minor", "patch"], "matchCurrentVersion": "!/^0/", "automerge": true}, {"matchDepTypes": ["devDependencies"], "matchUpdateTypes": ["minor", "patch"], "automerge": true}], "timezone": "Asia/Shanghai", "schedule": ["every weekend"], "prHourlyLimit": 2, "prConcurrentLimit": 5, "rangeStrategy": "bump", "semanticCommits": "enabled", "semanticCommitType": "chore", "semanticCommitScope": "deps", "dependencyDashboard": true, "dependencyDashboardTitle": "📦 依赖更新状态", "major": {"dependencyDashboardApproval": true}}