# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
*.tsbuildinfo

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Environment variables
.env
.env.local
.env.*.local

# Logs
logs/
*.log

# Testing
coverage/

# Temporary files
*.tmp
*.temp

# private docs
recommend-mcp-prd.md
scripts/

# Optional: package manager locks
# Uncomment if you want to ignore these
# package-lock.json
# yarn.lock