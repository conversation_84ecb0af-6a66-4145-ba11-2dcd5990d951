#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YouTube transkriptu gavimo skriptas
"""

from youtube_transcript_api import YouTubeTranscriptApi
import re
import sys

def get_video_id(url):
    """Ištraukia video ID iš YouTube URL"""
    video_id_match = re.search(r'(?:v=|\/)([0-9A-Za-z_-]{11}).*', url)
    if video_id_match:
        return video_id_match.group(1)
    return None

def get_transcript(video_url):
    """Gauna video transkripciją"""
    try:
        video_id = get_video_id(video_url)
        if not video_id:
            return "Klaida: Nepavyko ištraukti video ID iš URL"
        
        # Pabandome gauti transkripciją lietuvių kalba, jei ne - anglų
        try:
            transcript = YouTubeTranscriptApi.get_transcript(video_id, languages=['lt'])
        except:
            try:
                transcript = YouTubeTranscriptApi.get_transcript(video_id, languages=['en'])
            except:
                transcript = YouTubeTranscriptApi.get_transcript(video_id)
        
        # Sujungiame visus tekstus į vieną eilutę
        full_text = " ".join([entry['text'] for entry in transcript])
        return full_text
        
    except Exception as e:
        return f"Klaida gaunant transkripciją: {str(e)}"

if __name__ == "__main__":
    video_url = "https://www.youtube.com/watch?v=AGWyx96lP8U&t=15s"
    transcript = get_transcript(video_url)
    print(transcript)