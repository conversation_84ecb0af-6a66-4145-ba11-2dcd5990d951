# Programavimo Projektų Planavimo Darbo Planas
*Pagal YouTube video: "Complete Process for Planning Coding Projects"*

## A<PERSON>žvalga
Sistemingas 9 žingsnių procesas programavimo projektų planavimui, kuris padeda išvengti scope creep, sutaupyti laiko ir sukurti aiškų vykdymo planą.

## 🎯 1. <PERSON><PERSON><PERSON>: Pradėk nuo tikslo (Start from Your Goal)

### Pagrindiniai klausimai:
- [ ] **Kodėl kuriu šį projektą?**
  - Portfolio projektui
  - Kliento poreikiams
  - Hobio projektui
  - Startupui plėtoti
  - Mokymosi tikslams

- [ ] **Kam skirtas šis projektas?**
  - Identifikuoti tikslinę auditoriją
  - Suprasti vartotojų poreikius
  - Apibrėžti value proposition

- [ ] **Kas darys projekt<PERSON> vertingu?**
  - Kokie problemos sprendžiami
  - Koks unikalus vertės pasiūlymas
  - Kaip matuosime sėkmę

### Tikslų pavyzdžiai:
- **YouTube tutorial**: <PERSON>g<PERSON><PERSON> supranta<PERSON> koda<PERSON>, i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> funkcionalumai, pl<PERSON><PERSON>ji<PERSON> galim<PERSON>
- **Kliento projektas**: Nepriekaištingas veikimas, testai, švarus dizainas, verslo problemų sprendimas
- **Portfolio projektas**: Technologijų demonstravimas, code quality, funkcionalumas

---

## 📝 2. Žingsnis: Rašyk vartotojų istorijas (Write User Stories)

### User-centric dizaino principai:
- [ ] Aprašyti, ką vartotojas galės daryti su aplikacija
- [ ] Vengti techninio žargono šiame etape
- [ ] Susikoncentruoti į vartotojo perspektyvą
- [ ] Parašyti 10-20 pagrindinių user stories

### User Stories template:
```
Kaip [vartotojo tipas], aš noriu [funkcionalumo], kad galėčiau [tikslas].

Pavyzdžiai:
- Vartotojas turėtų galėti įkelti failą
- Vartotojas turėtų galėti sukurti paskyrą  
- Vartotojas turėtų galėti peržiūrėti dashboard
- Vartotojas turėtų galėti filtruoti rezultatus
- Vartotojas turėtų galėti eksportuoti duomenis
```

### Kategorijos:
- [ ] **Core funkcionalumas** (būtina)
- [ ] **Nice-to-have** funkcionalumas (pageidautina)
- [ ] **Future features** (ateities planai)

---

## 🗃️ 3. Žingsnis: Apibrėžk duomenų modelius (Define Data Models)

### Duomenų planavimas:
- [ ] Identifikuoti visus duomenų tipus
- [ ] Nustatyti ryšius tarp duomenų
- [ ] Sukurti aukšto lygio schema
- [ ] Vengti konkretaus DB tipo pasirinkimo šiame etape

### Pavyzdys - Blog sistema:
```
Models:
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    Users    │    │    Posts    │    │  Comments   │
├─────────────┤    ├─────────────┤    ├─────────────┤
│ id          │◄──┐│ id          │◄──┐│ id          │
│ name        │   ││ title       │   ││ content     │
│ email       │   ││ content     │   ││ user_id     │
│ created_at  │   ││ user_id     │───┘│ post_id     │
└─────────────┘   ││ created_at  │    │ created_at  │
                  │└─────────────┘    └─────────────┘
                  └─────────────────────────────────┘

Relationships:
- Users have many Posts
- Posts have many Comments  
- Comments belong to Users and Posts
```

### Tikrinimo klausimai:
- [ ] Ar reikia papildomų modelių (pvz., Categories)?
- [ ] Ar visi reikalingi laukai įtraukti?
- [ ] Ar ryšiai tarp modelių aiškūs?

---

## 🚀 4. Žingsnis: Apibrėžk MVP (Minimum Viable Product)

### MVP principai:
- [ ] **Ruthless prioritizavimas** - pašalinti 50% funkcionalumų
- [ ] Klausti: "Ar absoliučiai reikia šito funkcionalumo?"
- [ ] Jei atsakymas "Ne" - išmesti iš MVP
- [ ] Koncentruotis į mažiausią veikiančią versiją

### MVP pavyzdys - Task Management App:
```
✅ MVP funkcionalumas:
- Kurti užduotis
- Pažymėti kaip atliktas
- Ištrinti užduotis

❌ Išmesti iš MVP:
- Kategorijos
- Due dates
- Bendradarbiavimo funkcijos
- Fancy dashboard
- Notifications
- File attachments
```

### Scope creep prevencija:
- [ ] Dokumentuoti, kas NEįeina į MVP
- [ ] Sukurti "Version 2" funkcionalumų sąrašą
- [ ] Laikytis MVP plano kūrimo metu

---

## ✏️ 5. Žingsnis: Nupiešk paprastą prototipą (Draw Simple Wireframes)

### Wireframing gairės:
- [ ] Naudoti popierių arba paprastus įrankius
- [ ] NESIRŪPINTI dizaino grožiu
- [ ] Koncentruotis į layout ir flow
- [ ] Stick figures ir rectangles - puiku!

### Ką įtraukti į wireframes:
- [ ] Pagrindinių puslapių layout
- [ ] Navigacijos flow
- [ ] Login/registro formos vietos
- [ ] Pagrindinių veiksmų mygtukai
- [ ] Content organizavimas

### Tikrinimo klausimai:
- [ ] Ar navigacija logiška?
- [ ] Ar per daug clickų iki tikslo?
- [ ] Ar user flow aiškus?
- [ ] Ar mobili versija veiks?

---

## 🔮 6. Žingsnis: Pagalvok apie ateities viziją (Consider Project Future)

### Ateities planavimas:
- [ ] **Weekend hobby** - greitas prototypas, minimali infrastruktūra
- [ ] **Portfolio project** - švarus kodas, gera dokumentacija
- [ ] **Scalable product** - robust architektūra, testing
- [ ] **Client project** - production-ready, maintenance

### Skalės apsvarstymai:
```
┌─────────────────┬─────────────────┬─────────────────┐
│ Hobby Project   │ Portfolio       │ Production App  │
├─────────────────┼─────────────────┼─────────────────┤
│ SQLite          │ PostgreSQL      │ Distributed DB  │
│ Single server   │ VPS hosting     │ Cloud services  │
│ Basic security  │ JWT auth        │ Enterprise auth │
│ No tests        │ Unit tests      │ Full test suite │
│ Local deploy    │ Docker          │ CI/CD pipeline  │
└─────────────────┴─────────────────┴─────────────────┘
```

### Strateginiai klausimai:
- [ ] Kiek vartotojų tikimasi?
- [ ] Ar reiks plėsti funkcionalumą?
- [ ] Koks maintenance commitment?
- [ ] Kokia bus deployment strategija?

---

## 🏗️ 7. Žingsnis: Apibrėžk komponentų architektūrą (Define Components)

### Architektūriniai sprendimai:
- [ ] **Script application**
  - Python failas
  - Cron job / scheduler
  - Local execution

- [ ] **Web application**
  - Frontend (React/Vue/Angular)
  - Backend API (REST/GraphQL)
  - Database
  - Authentication system

- [ ] **Mobile application**
  - Native (iOS/Android)
  - Cross-platform (React Native/Flutter)
  - Backend services
  - Push notifications

- [ ] **Browser extension**
  - Content scripts
  - Background workers
  - Popup interfaces
  - Storage systems

### Komponentų schema:
```
Web App pavyzdys:
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  Frontend   │    │   Backend   │    │  Database   │
│             │◄──►│             │◄──►│             │
│ React/Vue   │    │ Node/Python │    │ PostgreSQL  │
│ State Mgmt  │    │ REST API    │    │ Redis cache │
└─────────────┘    └─────────────┘    └─────────────┘
       │                    │
       ▼                    ▼
┌─────────────┐    ┌─────────────┐
│    CDN      │    │   Auth      │
│             │    │             │
│ Static      │    │ JWT/OAuth   │
│ Assets      │    │ Sessions    │
└─────────────┘    └─────────────┘
```

---

## 🛠️ 8. Žingsnis: Pasirink tech stack (Pick Your Stack)

### Stack pasirinkimo principai:
- [ ] **Pirmas projektas, paskui įrankiai** (ne atvirkščiai)
- [ ] Pasirinkti paprasčiausią stack'ą, kuris sprendžia problemą
- [ ] Naudoti žinomas technologijas (nebent mokymasis yra tikslas)
- [ ] Patikrinti deployment galimybes

### Frontend technologijos:
```
┌─────────────────┬─────────────────┬─────────────────┐
│ Beginner        │ Intermediate    │ Advanced        │
├─────────────────┼─────────────────┼─────────────────┤
│ HTML/CSS/JS     │ React/Vue       │ Next.js/Nuxt   │
│ Bootstrap       │ TypeScript      │ Micro-frontends │
│ jQuery          │ State mgmt      │ SSR/SSG         │
└─────────────────┴─────────────────┴─────────────────┘
```

### Backend technologijos:
```
┌─────────────────┬─────────────────┬─────────────────┐
│ Python          │ Node.js         │ Go/Rust         │
├─────────────────┼─────────────────┼─────────────────┤
│ Flask/FastAPI   │ Express/Nest    │ Gin/Actix       │
│ Django          │ Koa             │ Native          │
│ SQLAlchemy      │ Prisma/TypeORM  │ Performance     │
└─────────────────┴─────────────────┴─────────────────┘
```

### Duomenų bazės:
```
┌─────────────────┬─────────────────┬─────────────────┐
│ Simple          │ Structured      │ Complex         │
├─────────────────┼─────────────────┼─────────────────┤
│ SQLite          │ PostgreSQL      │ Distributed     │
│ File storage    │ MySQL           │ MongoDB         │
│ CSV/JSON        │ Redis cache     │ Elasticsearch   │
└─────────────────┴─────────────────┴─────────────────┘
```

### Deployment galimybės:
- [ ] **Free tier**: Vercel, Netlify, Heroku
- [ ] **VPS**: DigitalOcean, Linode, AWS EC2
- [ ] **Serverless**: AWS Lambda, Cloudflare Workers
- [ ] **Container**: Docker + Kubernetes

### Stack pavyzdžiai projektų tipams:

#### 🏃‍♂️ Quick Prototype:
- Frontend: HTML/CSS/JavaScript
- Backend: Python Flask
- Database: SQLite
- Deploy: Heroku free tier

#### 💼 Portfolio Project:
- Frontend: React + TypeScript
- Backend: Node.js + Express
- Database: PostgreSQL
- Deploy: Vercel + Railway

#### 🚀 Production App:
- Frontend: Next.js
- Backend: Python FastAPI
- Database: PostgreSQL + Redis
- Deploy: AWS/GCP + Docker

---

## 💻 9. Žingsnis: Kūrimo procesas (Development Process)

### Kūrimo etapai:

#### 1. **Projekto pagrindų sukūrimas** 
- [ ] Katalogų struktūra
- [ ] Version control (Git)
- [ ] Development environment
- [ ] Package management setup
- [ ] Basic configuration files

```
project-structure/
├── frontend/
│   ├── src/
│   ├── public/
│   └── package.json
├── backend/
│   ├── src/
│   ├── tests/
│   └── requirements.txt
├── docs/
├── .gitignore
├── README.md
└── docker-compose.yml
```

#### 2. **Duomenų bazės setup**
- [ ] Database schema kūrimas
- [ ] Models/entities apibrėžimas
- [ ] Migration files
- [ ] Seed data
- [ ] Database connections testing

#### 3. **Backend API kūrimas**
- [ ] Core endpoints
- [ ] CRUD operacijos
- [ ] Authentication middleware
- [ ] Error handling
- [ ] API testing (Postman/Insomnia)

```
API Endpoints pavyzdys:
GET    /api/users
POST   /api/users
GET    /api/users/:id
PUT    /api/users/:id
DELETE /api/users/:id

POST   /api/auth/login
POST   /api/auth/register
POST   /api/auth/logout
```

#### 4. **Frontend interface**
- [ ] Basic components
- [ ] Routing setup
- [ ] API integration
- [ ] State management
- [ ] Error handling
- [ ] Loading states

#### 5. **Integration testing**
- [ ] End-to-end user flows
- [ ] API + Frontend integration
- [ ] Authentication flows
- [ ] Error scenarios
- [ ] Performance testing

#### 6. **Deployment**
- [ ] Environment variables setup
- [ ] Production database
- [ ] Build optimization
- [ ] Domain configuration
- [ ] SSL certificates
- [ ] Monitoring setup

#### 7. **Iteration & refinement**
- [ ] User feedback collection
- [ ] Bug fixes
- [ ] Performance optimization
- [ ] Feature additions
- [ ] Documentation updates

### Kūrimo geriausi praktikos:

#### Test-driven approach:
- [ ] Rašyti mažus kodo gabalus
- [ ] Testuoti funkcionalumą iš karto
- [ ] Fix bugs prieš moving forward
- [ ] Deploy early and often

#### Version control workflow:
```
git workflow:
main branch (production)
├── develop branch (integration)
    ├── feature/user-auth
    ├── feature/dashboard
    └── bugfix/login-error
```

#### Code quality:
- [ ] Consistent naming conventions
- [ ] Code comments where needed
- [ ] Error handling everywhere
- [ ] Security best practices
- [ ] Performance considerations

---

## ⚡ Greitas planavimo checklist (Express Planning)

Kai reikia greitai suplanuoti projektą (5-10 min):

### 2-minutės MVP:
- [ ] Vienas tikslas vienu sakiniu
- [ ] 3-5 pagrindinės user stories
- [ ] 1-2 duomenų modeliai
- [ ] Paprasčiausias stack kurį žinau

### 5-minutės planas:
- [ ] Detalūs user stories
- [ ] Basic wireframe sketch
- [ ] Tech stack decision
- [ ] Deployment path check

### 10-minutės planas:
- [ ] Pilnas 9-žingsnių procesas
- [ ] Detailed component architecture
- [ ] Risk assessment
- [ ] Timeline estimation

---

## 🎯 Pagrindinės žinutės

### Planavimo vertė:
> "Valanda planavimo sutaupo dienas kodavimo"

### Auksinės taisyklės:
1. **Tikslas pirma** - tech stack antra
2. **MVP ruthlessly** - 50% funkcionalumų šalin
3. **Paprasta geriau** - complexity kills projects
4. **Deploy path svarbus** - patikrinti prieš pradedant
5. **Žinomas tech** - nebent mokymasis yra tikslas

### Dažniausios klaidos:
- ❌ Straight to code without planning
- ❌ Overengineering simple projects  
- ❌ Underengineering scalable apps
- ❌ Scope creep during development
- ❌ Picking unknown technologies
- ❌ No deployment consideration

### Sėkmės faktoriai:
- ✅ Clear goal and user focus
- ✅ Ruthless MVP scoping
- ✅ Simple, known technologies
- ✅ Early and frequent testing
- ✅ Iterative development
- ✅ Deploy early and often

---

## 📚 Papildomi ištekliai

### Planning įrankiai:
- **Wireframing**: Figma, Balsamiq, draw.io, Paper
- **Project management**: Trello, Notion, GitHub Projects
- **Database design**: dbdiagram.io, draw.io
- **Architecture**: Lucidchart, draw.io

### Template failai:
- Project planning template
- User stories template  
- MVP definition worksheet
- Tech stack decision matrix

---

*Šis planavimo procesas tinka visų dydžių projektams - nuo weekend hobby iki enterprise aplikacijų. Svarbu pritaikyti proceso detalumą pagal projekto mastą.*