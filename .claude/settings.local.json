{"permissions": {"allow": ["<PERSON><PERSON>(env)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(npm install:*)", "<PERSON><PERSON>(mcp:*)", "<PERSON><PERSON>(python3:*)", "WebFetch(domain:mcpservers.org)", "WebFetch(domain:github.com)", "Bash(npx -y @modelcontextprotocol/server-playwright:*)", "Bash(npx:*)", "WebFetch(domain:mcp.so)", "Bash(pip install:*)", "Bash(env FIRECRAWL_API_KEY:*)", "<PERSON><PERSON>(git clone:*)", "Bash(npm run build:*)", "Bash(node dist/cli.js --help)", "Bash(pnpm install:*)", "Bash(pnpm build:*)", "Bash(node:*)", "Bash(pnpm zip:*)", "Bash(pip3 install:*)", "WebFetch(domain:www.delfi.lt)", "Bash(rm:*)", "<PERSON><PERSON>(open:*)"], "deny": []}}