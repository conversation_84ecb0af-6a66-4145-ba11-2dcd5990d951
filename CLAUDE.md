# CLAUDE.md

## Projekto aprašymas
Šis failas skirtas Claude AI asistentui geriau suprasti projekto struktūrą ir reikalavimus.

## Kalbos reikalavimai
- Visa komunikacija turi vykti lietuvių kalba
- Komentarai kode taip pat turi būti lietuvių kalba

## Projekto struktūra
- `test_python_script.py` - Python testas
- `test_data.csv` - Testavimo duomenys
- `testas/` - Testų katalogas

## Failų organizavimo taisyklės
- Visi failai TURI būti priskirti į atitinkamas direktorijas pagal jų paskirtį
- NEGALIMA palikti failų be direktorijos šakniniame kataloge
- Kiekvienas failas turi turėti aiškią vietą projekto struktūroje

## MCP Serverių konfigūracija
Šiame projekte sukonfigūruoti šie MCP (Model Context Protocol) serveriai:

### Aktyvūs MCP serveriai:
- **bright-data** - Web scraping su Bright Data API (gali nuskaityti bet kokias svetaines)
- **puppeteer** - <PERSON>rowser automatizavimas (gali kontroliuoti naršyklę)
- **playwright-official** - Microsoft Playwright browser automatizavimas
- **playwright-community** - Community Playwright serveris
- **firecrawl** - Web crawling ir turinio gavimas
- **sequential-thinking** - Struktūruotas mąstymas
- **sentry** - Error tracking ir monitoring

### Lokalūs MCP serveriai:
- **figma-context** - Figma integracija ir konteksto gavimas
- **youtube-transcript** - YouTube vaizdo įrašų transkriptų gavimas

### MCP serverių panaudojimas:
- **Web scraping**: bright-data, firecrawl, puppeteer, playwright gali nuskaityti bet kokias svetaines, įskaitant Delfi.lt
- **Browser automatizavimas**: puppeteer ir playwright gali atlikti sudėtingus veiksmus naršyklėje
- **Specialūs integracijos**: figma-context ir youtube-transcript specializuotiems uždaviniams

## Komandos MCP serverių valdymui:
- Restartinti Claude Desktop kad MCP serveriai būtų aktyvūs
- MCP serveriai automatiškai kraunami paleidžiant Claude Desktop

## Instrukcijos
Claude turi kalbėti tik lietuviškai su šiuo projektu.

## Užduočių valdymas
- Prašyti Claude sukurti todo sąrašą dirbant su sudėtingomis užduotimis, kad galėtų sekti pažangą ir išlikti teisingu keliu