# Claude Code Execution Test Instructions

## 🎯 T<PERSON><PERSON>s
<PERSON>šbandyti Claude Desktop app code execution galimybes

## 📁 Sukurti failai
1. `test_data.csv` - Pardavimų duomenys testavimui
2. `test_python_script.py` - Python skriptas duomenų analizei
3. `claude_test_instructions.md` - <PERSON><PERSON> instrukcijos

## 🚀 Kaip testuoti

### 1. Atidarykite Claude Desktop App
- Jau turėtumėte matėti atidarytą Claude aplik<PERSON>iją
- <PERSON><PERSON> ne, atidarykite iš Applications aplanko

### 2. Išbandykite šiuos pavyzdžius Claude Desktop app:

#### A) Paprastas Python kodas
Nukopijuokite ir įklijuokite į Claude:

```
Paleisk šį Python kodą:

print("Sveiki! Testuojame Claude code execution")
import datetime
print(f"Dabartinis laikas: {datetime.datetime.now()}")

# Paprastas skaičiavimas
numbers = [1, 2, 3, 4, 5]
suma = sum(numbers)
print(f"Skaičių {numbers} suma: {suma}")
```

#### B) Duomenų analizė
```
Nuskaityk ir analizuok failą test_data.csv:

import pandas as pd
import matplotlib.pyplot as plt

# Skaitome duomenis
df = pd.read_csv('test_data.csv')
print("Duomenų apžvalga:")
print(df.head())
print(f"\nIš viso įrašų: {len(df)}")

# Statistika
print(f"Vidutiniai pardavimai: {df['sales'].mean():.2f}")
print(f"Pardavimai pagal regionus:")
print(df.groupby('region')['sales'].sum())
```

#### C) Vizualizacija
```
Sukurk grafiką iš test_data.csv duomenų:

import pandas as pd
import matplotlib.pyplot as plt

df = pd.read_csv('test_data.csv')

plt.figure(figsize=(10, 6))
region_sales = df.groupby('region')['sales'].sum()
plt.bar(region_sales.index, region_sales.values)
plt.title('Pardavimai pagal regionus')
plt.xlabel('Regionas')
plt.ylabel('Pardavimai')
plt.show()
```

#### D) Pilnas test skriptas
```
Paleisk pilną test skriptą:

exec(open('test_python_script.py').read())
```

### 3. Ką turėtumėte pamatyti

✅ **Sėkmingas rezultatas:**
- Claude paleis kodą ir parodys rezultatus
- Matysite duomenų analizės rezultatus
- Bus sukurti grafikai ir vizualizacijos
- Gausite statistikos suvestinę

❌ **Jei neveikia:**
- Patikrinkite, ar turite naujausią Claude Desktop versją
- Įsitikinkite, kad code execution funkcija yra įjungta
- Bandykite paprastesnį kodą pirmiausia

### 4. Papildomi testai

#### Failų kūrimas:
```
Sukurk naują failą:

with open('test_output.txt', 'w') as f:
    f.write('Claude code execution veikia!\n')
    f.write(f'Testas atliktas: {datetime.datetime.now()}\n')

print("Failas sukurtas!")
```

#### Matematikos uždaviniai:
```
Išspręsk matematikos uždavinį:

import numpy as np

# Kvadratinės lygties sprendimas: x² - 5x + 6 = 0
a, b, c = 1, -5, 6
discriminant = b**2 - 4*a*c
x1 = (-b + np.sqrt(discriminant)) / (2*a)
x2 = (-b - np.sqrt(discriminant)) / (2*a)

print(f"Sprendiniai: x1 = {x1}, x2 = {x2}")
```

## 🔧 Troubleshooting

### Jei code execution neveikia:
1. Atnaujinkite Claude Desktop app
2. Patikrinkite Settings > Features > Code Execution
3. Pabandykite iš naujo paleisti aplikaciją
4. Įsitikinkite, kad turite interneto ryšį

### Jei trūksta Python bibliotekų:
Claude Desktop app turi įmontuotas pagrindines bibliotekas, bet jei ko nors trūksta:
```
pip install pandas matplotlib numpy
```

## 📝 Rezultatų vertinimas

**Puiku (10/10):** Viskas veikia, grafikai rodomi, duomenys analizuojami
**Gerai (7-9/10):** Kodas veikia, bet kai kurios funkcijos neveikia
**Vidutiniškai (4-6/10):** Paprastas kodas veikia, sudėtingesnis ne
**Blogai (1-3/10):** Tik labai paprastas kodas veikia
**Neveikia (0/10):** Jokio kodo paleisti nepavyksta

Sėkmės testuojant! 🚀
