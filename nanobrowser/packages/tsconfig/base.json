{"$schema": "https://json.schemastore.org/tsconfig", "display": "Base", "compilerOptions": {"allowJs": true, "noEmit": true, "module": "esnext", "downlevelIteration": true, "isolatedModules": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "noImplicitReturns": true, "jsx": "react-jsx", "lib": ["DOM", "ESNext"]}}