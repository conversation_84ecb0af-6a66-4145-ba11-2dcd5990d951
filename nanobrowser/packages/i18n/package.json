{"name": "@extension/i18n", "version": "0.1.6", "description": "chrome extension - internationalization", "private": true, "sideEffects": false, "files": ["dist/**"], "types": "index.ts", "main": "./dist/index.js", "scripts": {"clean:bundle": "<PERSON><PERSON><PERSON> dist", "clean:node_modules": "pnpx rimraf node_modules", "clean:turbo": "rimraf .turbo", "clean": "pnpm clean:bundle && pnpm clean:node_modules && pnpm clean:turbo", "genenrate-i8n": "node genenrate-i18n.mjs", "ready": "pnpm genenrate-i8n && node build.dev.mjs", "build": "pnpm genenrate-i8n && node build.prod.mjs", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "pnpm lint --fix", "prettier": "prettier . --write --ignore-path ../../.prettierignore", "type-check": "tsc --noEmit"}, "devDependencies": {"@extension/tsconfig": "workspace:*", "@extension/hmr": "workspace:*"}}