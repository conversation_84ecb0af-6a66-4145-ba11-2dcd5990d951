{"name": "@extension/schema-utils", "version": "0.1.6", "description": "JSON schema and related helpers for tools", "private": true, "type": "module", "sideEffects": false, "files": ["dist/**"], "types": "index.ts", "main": "./dist/index.js", "scripts": {"clean:bundle": "<PERSON><PERSON><PERSON> dist", "clean:node_modules": "pnpx rimraf node_modules", "clean:turbo": "rimraf .turbo", "clean": "pnpm clean:bundle && pnpm clean:node_modules && pnpm clean:turbo", "ready": "node build.mjs", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "pnpm lint --fix", "prettier": "prettier . --write --ignore-path ../../.prettierignore", "type-check": "tsc --noEmit", "example:convert": "pnpm run ready && node dist/examples/convert.js", "example:flatten": "pnpm run ready && node dist/examples/flatten.js"}, "devDependencies": {"@extension/tsconfig": "workspace:*"}}