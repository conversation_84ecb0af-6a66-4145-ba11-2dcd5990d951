#app-container {
  text-align: center;
  width: 100vw;
  height: 100vh;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

.App {
  width: 100vw;
  height: 100vh;
  font-size: calc(10px + 2vmin);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

code {
  background: rgba(148, 163, 184, 0.5);
  border-radius: 0.25rem;
  padding: 0.2rem 0.5rem;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  code {
    background: rgba(30, 58, 138, 0.4);
    color: #7dd3fc;
  }
  
  .dark-mode-text {
    color: #e2e8f0 !important; /* slate-200 */
  }
  
  .dark-mode-bg {
    background-color: #1e293b !important; /* slate-800 */
  }
  
  .dark-mode-border {
    border-color: #475569 !important; /* slate-600 */
  }
}
