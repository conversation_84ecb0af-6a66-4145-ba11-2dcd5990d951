{"name": "@extension/options", "version": "0.1.6", "description": "chrome extension - options", "private": true, "sideEffects": true, "files": ["dist/**"], "scripts": {"clean:node_modules": "pnpx rimraf node_modules", "clean:turbo": "rimraf .turbo", "clean": "pnpm clean:turbo && pnpm clean:node_modules", "build": "vite build", "dev": "cross-env __DEV__=true vite build --mode development", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "pnpm lint --fix", "prettier": "prettier . --write --ignore-path ../../.prettierignore", "type-check": "tsc --noEmit"}, "dependencies": {"@extension/shared": "workspace:*", "@extension/storage": "workspace:*", "@extension/ui": "workspace:*"}, "devDependencies": {"@extension/tailwindcss-config": "workspace:*", "@extension/tsconfig": "workspace:*", "@extension/vite-config": "workspace:*", "postcss-load-config": "^6.0.1", "cross-env": "^7.0.3"}, "postcss": {"plugins": {"tailwindcss": {}, "autoprefixer": {}}}}