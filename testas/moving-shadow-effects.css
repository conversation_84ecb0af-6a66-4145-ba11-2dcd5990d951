/* JUDANTIS ŠEŠĖLIS - <PERSON><PERSON><PERSON><PERSON> efektai */

/* Additional moving shadow layers for more depth */
.hero-card {
  position: relative;
}

/* Secondary shadow layer */
.hero-card .content::before {
  content: '';
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  background: radial-gradient(
    circle 300px at var(--mouse-x, 50%) var(--mouse-y, 50%),
    rgba(0, 0, 0, 0.08) 0%,
    rgba(0, 0, 0, 0.04) 40%,
    transparent 70%
  );
  border-radius: 20px;
  pointer-events: none;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
  animation: breathingShadow 8s ease-in-out infinite;
}

.hero-card:hover .content::before {
  opacity: 1;
}

@keyframes breathingShadow {
  0%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* Pulsing inner shadow */
.floating-elements::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80%;
  height: 80%;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(0, 0, 0, 0.05) 30%,
    transparent 70%
  );
  border-radius: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 0;
  animation: pulsingInnerShadow 6s ease-in-out infinite;
}

@keyframes pulsingInnerShadow {
  0%, 100% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 0.2;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.6;
  }
}

/* Organic flowing shadow */
.hero-card .floating-elements::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(
      ellipse 200px 100px at 20% 30%,
      rgba(0, 0, 0, 0.1) 0%,
      transparent 60%
    ),
    radial-gradient(
      ellipse 150px 80px at 80% 70%,
      rgba(0, 0, 0, 0.08) 0%,
      transparent 50%
    );
  pointer-events: none;
  z-index: 0;
  animation: organicFlow 15s ease-in-out infinite;
  mix-blend-mode: multiply;
}

@keyframes organicFlow {
  0% {
    transform: translateX(0) translateY(0) rotate(0deg);
    opacity: 0.3;
  }
  25% {
    transform: translateX(30px) translateY(-20px) rotate(90deg);
    opacity: 0.5;
  }
  50% {
    transform: translateX(20px) translateY(30px) rotate(180deg);
    opacity: 0.7;
  }
  75% {
    transform: translateX(-20px) translateY(20px) rotate(270deg);
    opacity: 0.4;
  }
  100% {
    transform: translateX(0) translateY(0) rotate(360deg);
    opacity: 0.3;
  }
}

/* Interactive shadow that follows cursor */
.hero-card.mouse-tracking::before {
  background: 
    radial-gradient(
      circle 400px at var(--mouse-x, 50%) var(--mouse-y, 50%),
      rgba(0, 0, 0, 0.12) 0%,
      rgba(0, 0, 0, 0.06) 30%,
      transparent 60%
    );
  transition: background 0.1s ease;
}

/* Slow morph shadow */
.hero-card .demo-button::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  width: 150%;
  height: 30px;
  background: radial-gradient(
    ellipse at center,
    rgba(0, 0, 0, 0.15) 0%,
    rgba(0, 0, 0, 0.08) 50%,
    transparent 100%
  );
  transform: translateX(-50%);
  border-radius: 50%;
  pointer-events: none;
  z-index: -1;
  animation: buttonShadowMorph 10s ease-in-out infinite;
}

@keyframes buttonShadowMorph {
  0%, 100% {
    width: 150%;
    height: 30px;
    opacity: 0.6;
    transform: translateX(-50%) scaleY(1);
  }
  25% {
    width: 200%;
    height: 40px;
    opacity: 0.4;
    transform: translateX(-50%) scaleY(0.7);
  }
  50% {
    width: 180%;
    height: 35px;
    opacity: 0.8;
    transform: translateX(-50%) scaleY(1.2);
  }
  75% {
    width: 160%;
    height: 25px;
    opacity: 0.5;
    transform: translateX(-50%) scaleY(0.9);
  }
}

/* Enhanced hover effects for shadows */
.hero-card:hover::after {
  animation-duration: 6s;
  opacity: 0.8;
  filter: blur(2px);
}

.hero-card:hover .floating-elements::before {
  animation-duration: 3s;
  opacity: 0.9;
}

.hero-card:hover .floating-elements::after {
  animation-duration: 8s;
  opacity: 0.6;
}