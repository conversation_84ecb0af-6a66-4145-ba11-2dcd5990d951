// JUDANTIS ŠEŠĖLIS - Interaktyvūs efektai

class MovingShadowEffects {
    constructor() {
        this.heroCard = document.querySelector('.hero-card');
        this.isMouseTracking = false;
        
        this.init();
        this.addMouseTracking();
        this.addInteractiveEffects();
    }

    init() {
        // Add mouse tracking class
        this.heroCard.classList.add('mouse-tracking');
        
        // Initialize CSS custom properties for mouse position
        this.heroCard.style.setProperty('--mouse-x', '50%');
        this.heroCard.style.setProperty('--mouse-y', '50%');
    }

    addMouseTracking() {
        this.heroCard.addEventListener('mousemove', (e) => {
            const rect = this.heroCard.getBoundingClientRect();
            const x = ((e.clientX - rect.left) / rect.width) * 100;
            const y = ((e.clientY - rect.top) / rect.height) * 100;
            
            // Update CSS custom properties for mouse position
            this.heroCard.style.setProperty('--mouse-x', `${x}%`);
            this.heroCard.style.setProperty('--mouse-y', `${y}%`);
            
            // Create dynamic shadow based on mouse position
            this.createDynamicShadow(x, y);
        });

        this.heroCard.addEventListener('mouseleave', () => {
            // Reset to center when mouse leaves
            this.heroCard.style.setProperty('--mouse-x', '50%');
            this.heroCard.style.setProperty('--mouse-y', '50%');
        });
    }

    createDynamicShadow(x, y) {
        // Create a temporary shadow element that follows mouse
        const existingShadow = this.heroCard.querySelector('.dynamic-shadow');
        if (existingShadow) {
            existingShadow.remove();
        }

        const shadowElement = document.createElement('div');
        shadowElement.className = 'dynamic-shadow';
        shadowElement.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(
                circle 300px at ${x}% ${y}%,
                rgba(0, 0, 0, 0.1) 0%,
                rgba(0, 0, 0, 0.05) 40%,
                transparent 70%
            );
            pointer-events: none;
            z-index: 1;
            border-radius: 16px;
            opacity: 0;
            transition: opacity 0.2s ease;
        `;

        this.heroCard.appendChild(shadowElement);

        // Fade in the shadow
        requestAnimationFrame(() => {
            shadowElement.style.opacity = '0.7';
        });

        // Remove after delay
        setTimeout(() => {
            if (shadowElement.parentNode) {
                shadowElement.style.opacity = '0';
                setTimeout(() => {
                    if (shadowElement.parentNode) {
                        shadowElement.remove();
                    }
                }, 200);
            }
        }, 1000);
    }

    addInteractiveEffects() {
        // Add click ripple with shadow
        this.heroCard.addEventListener('click', (e) => {
            this.createClickShadowRipple(e);
        });

        // Add breathing effect on focus
        this.heroCard.addEventListener('focusin', () => {
            this.addBreathingEffect();
        });

        this.heroCard.addEventListener('focusout', () => {
            this.removeBreathingEffect();
        });

        // Add scroll-based shadow intensity
        this.addScrollEffects();
    }

    createClickShadowRipple(event) {
        const rect = this.heroCard.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        const ripple = document.createElement('div');
        ripple.style.cssText = `
            position: absolute;
            left: ${x}px;
            top: ${y}px;
            width: 0;
            height: 0;
            background: radial-gradient(
                circle,
                rgba(0, 0, 0, 0.2) 0%,
                rgba(0, 0, 0, 0.1) 50%,
                transparent 100%
            );
            border-radius: 50%;
            pointer-events: none;
            z-index: 2;
            transform: translate(-50%, -50%);
            animation: shadowRipple 1s ease-out;
        `;

        this.heroCard.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 1000);
    }

    addBreathingEffect() {
        this.heroCard.style.animation = 'shadowBreathing 2s ease-in-out infinite';
    }

    removeBreathingEffect() {
        this.heroCard.style.animation = '';
    }

    addScrollEffects() {
        let lastScrollY = window.scrollY;

        window.addEventListener('scroll', () => {
            const currentScrollY = window.scrollY;
            const scrollDelta = currentScrollY - lastScrollY;
            
            // Adjust shadow intensity based on scroll direction
            const intensity = Math.min(Math.abs(scrollDelta) / 50, 1);
            
            const shadowElement = this.heroCard.querySelector('::after');
            if (shadowElement) {
                this.heroCard.style.setProperty('--scroll-intensity', intensity);
            }

            lastScrollY = currentScrollY;
        });
    }

    // Add CSS animations dynamically
    static addDynamicStyles() {
        const style = document.createElement('style');
        style.textContent = `
            @keyframes shadowRipple {
                0% {
                    width: 0;
                    height: 0;
                    opacity: 1;
                }
                100% {
                    width: 400px;
                    height: 400px;
                    opacity: 0;
                }
            }

            @keyframes shadowBreathing {
                0%, 100% {
                    filter: drop-shadow(0 0 20px rgba(0, 0, 0, 0.1));
                }
                50% {
                    filter: drop-shadow(0 0 40px rgba(0, 0, 0, 0.2));
                }
            }

            /* Enhanced shadow morphing */
            .hero-card::after {
                filter: blur(0px);
                transition: filter 0.3s ease, animation-duration 0.3s ease;
            }

            .hero-card:hover::after {
                filter: blur(1px);
                animation-duration: 8s;
            }

            /* Smooth shadow transitions */
            .hero-card .floating-elements::before,
            .hero-card .floating-elements::after {
                will-change: transform, opacity;
                backface-visibility: hidden;
            }

            /* Performance optimization */
            .dynamic-shadow {
                will-change: opacity;
                contain: layout style paint;
            }

            /* Responsive shadow adjustments */
            @media (max-width: 768px) {
                .hero-card::after {
                    animation-duration: 8s;
                    opacity: 0.4;
                }
                
                .hero-card .floating-elements::before {
                    animation-duration: 4s;
                }
            }

            /* Reduced motion support */
            @media (prefers-reduced-motion: reduce) {
                .hero-card::after,
                .hero-card .floating-elements::before,
                .hero-card .floating-elements::after {
                    animation: none !important;
                }
                
                .dynamic-shadow {
                    transition: none !important;
                }
            }
        `;
        
        document.head.appendChild(style);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    MovingShadowEffects.addDynamicStyles();
    new MovingShadowEffects();
    
    console.log('🌊 Judantis šešėlis aktyvuotas');
});

// Export for external use
window.MovingShadowEffects = MovingShadowEffects;