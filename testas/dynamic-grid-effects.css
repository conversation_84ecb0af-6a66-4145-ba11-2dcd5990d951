/* DINAMINIAI GRID EFEKTAI - Netolygus pranykimas */

/* Additional grid layers with enhanced edge fade */
body::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(#8b949e 1px, transparent 1px),
    linear-gradient(90deg, #8b949e 1px, transparent 1px);
  background-size: 48px 48px;
  opacity: 0.5;
  pointer-events: none;
  z-index: -2;
  
  /* Enhanced edge fade with organic patterns */
  mask: 
    /* Strong edge fade */
    linear-gradient(to right, transparent 0%, rgba(0,0,0,0.2) 8%, rgba(0,0,0,0.6) 20%, black 40%, black 60%, rgba(0,0,0,0.6) 80%, rgba(0,0,0,0.2) 92%, transparent 100%),
    linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.2) 8%, rgba(0,0,0,0.6) 20%, black 40%, black 60%, rgba(0,0,0,0.6) 80%, rgba(0,0,0,0.2) 92%, transparent 100%),
    /* Organic patterns within visible area */
    radial-gradient(circle 300px at 25% 30%, transparent 40%, rgba(0,0,0,0.8) 80%),
    radial-gradient(circle 250px at 75% 20%, transparent 30%, rgba(0,0,0,0.7) 70%),
    radial-gradient(circle 200px at 80% 70%, transparent 35%, rgba(0,0,0,0.75) 75%),
    radial-gradient(circle 180px at 20% 80%, transparent 25%, rgba(0,0,0,0.65) 65%),
    radial-gradient(circle 350px at 60% 50%, transparent 50%, rgba(0,0,0,0.9) 90%);
  -webkit-mask: 
    linear-gradient(to right, transparent 0%, rgba(0,0,0,0.2) 8%, rgba(0,0,0,0.6) 20%, black 40%, black 60%, rgba(0,0,0,0.6) 80%, rgba(0,0,0,0.2) 92%, transparent 100%),
    linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.2) 8%, rgba(0,0,0,0.6) 20%, black 40%, black 60%, rgba(0,0,0,0.6) 80%, rgba(0,0,0,0.2) 92%, transparent 100%),
    radial-gradient(circle 300px at 25% 30%, transparent 40%, rgba(0,0,0,0.8) 80%),
    radial-gradient(circle 250px at 75% 20%, transparent 30%, rgba(0,0,0,0.7) 70%),
    radial-gradient(circle 200px at 80% 70%, transparent 35%, rgba(0,0,0,0.75) 75%),
    radial-gradient(circle 180px at 20% 80%, transparent 25%, rgba(0,0,0,0.65) 65%),
    radial-gradient(circle 350px at 60% 50%, transparent 50%, rgba(0,0,0,0.9) 90%);
  
  animation: gridShift 20s ease-in-out infinite;
}

@keyframes gridShift {
  0%, 100% {
    transform: translate(0, 0);
    opacity: 0.4;
  }
  25% {
    transform: translate(5px, -3px);
    opacity: 0.6;
  }
  50% {
    transform: translate(-3px, 7px);
    opacity: 0.3;
  }
  75% {
    transform: translate(7px, -5px);
    opacity: 0.5;
  }
}

/* Animated disappearing grid cells with edge fade */
.container::before {
  content: '';
  position: absolute;
  top: -200px;
  left: -200px;
  right: -200px;
  bottom: -200px;
  background-image: 
    linear-gradient(#656d76 0.8px, transparent 0.8px),
    linear-gradient(90deg, #656d76 0.8px, transparent 0.8px);
  background-size: 48px 48px;
  opacity: 0.6;
  pointer-events: none;
  z-index: -1;
  
  /* Complex mask with edge fade and animated holes */
  mask: 
    /* Strong edge vignette */
    radial-gradient(ellipse 120% 100% at center, black 30%, rgba(0,0,0,0.8) 50%, rgba(0,0,0,0.4) 75%, transparent 100%),
    /* Side fades */
    linear-gradient(to right, transparent 0%, rgba(0,0,0,0.3) 12%, black 25%, black 75%, rgba(0,0,0,0.3) 88%, transparent 100%),
    linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.3) 12%, black 25%, black 75%, rgba(0,0,0,0.3) 88%, transparent 100%),
    /* Animated holes */
    radial-gradient(circle 80px at 30% 25%, transparent 60%, rgba(0,0,0,0.9) 100%),
    radial-gradient(circle 60px at 70% 30%, transparent 50%, rgba(0,0,0,0.9) 100%),
    radial-gradient(circle 100px at 25% 70%, transparent 70%, rgba(0,0,0,0.9) 100%),
    radial-gradient(circle 70px at 80% 75%, transparent 55%, rgba(0,0,0,0.9) 100%),
    radial-gradient(circle 90px at 50% 60%, transparent 65%, rgba(0,0,0,0.9) 100%),
    radial-gradient(circle 120px at 15% 50%, transparent 75%, rgba(0,0,0,0.9) 100%),
    radial-gradient(circle 85px at 85% 40%, transparent 60%, rgba(0,0,0,0.9) 100%);
  -webkit-mask: 
    radial-gradient(ellipse 120% 100% at center, black 30%, rgba(0,0,0,0.8) 50%, rgba(0,0,0,0.4) 75%, transparent 100%),
    linear-gradient(to right, transparent 0%, rgba(0,0,0,0.3) 12%, black 25%, black 75%, rgba(0,0,0,0.3) 88%, transparent 100%),
    linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.3) 12%, black 25%, black 75%, rgba(0,0,0,0.3) 88%, transparent 100%),
    radial-gradient(circle 80px at 30% 25%, transparent 60%, rgba(0,0,0,0.9) 100%),
    radial-gradient(circle 60px at 70% 30%, transparent 50%, rgba(0,0,0,0.9) 100%),
    radial-gradient(circle 100px at 25% 70%, transparent 70%, rgba(0,0,0,0.9) 100%),
    radial-gradient(circle 70px at 80% 75%, transparent 55%, rgba(0,0,0,0.9) 100%),
    radial-gradient(circle 90px at 50% 60%, transparent 65%, rgba(0,0,0,0.9) 100%),
    radial-gradient(circle 120px at 15% 50%, transparent 75%, rgba(0,0,0,0.9) 100%),
    radial-gradient(circle 85px at 85% 40%, transparent 60%, rgba(0,0,0,0.9) 100%);
  
  animation: disappearingCells 25s ease-in-out infinite;
}

@keyframes disappearingCells {
  0% {
    mask-size: 0 0, 0 0, 0 0, 0 0, 0 0, 0 0, 0 0;
    -webkit-mask-size: 0 0, 0 0, 0 0, 0 0, 0 0, 0 0, 0 0;
  }
  15% {
    mask-size: 160px 160px, 0 0, 0 0, 0 0, 0 0, 0 0, 0 0;
    -webkit-mask-size: 160px 160px, 0 0, 0 0, 0 0, 0 0, 0 0, 0 0;
  }
  30% {
    mask-size: 160px 160px, 120px 120px, 200px 200px, 0 0, 0 0, 0 0, 0 0;
    -webkit-mask-size: 160px 160px, 120px 120px, 200px 200px, 0 0, 0 0, 0 0, 0 0;
  }
  45% {
    mask-size: 160px 160px, 120px 120px, 200px 200px, 140px 140px, 180px 180px, 0 0, 0 0;
    -webkit-mask-size: 160px 160px, 120px 120px, 200px 200px, 140px 140px, 180px 180px, 0 0, 0 0;
  }
  60% {
    mask-size: 160px 160px, 120px 120px, 200px 200px, 140px 140px, 180px 180px, 240px 240px, 170px 170px;
    -webkit-mask-size: 160px 160px, 120px 120px, 200px 200px, 140px 140px, 180px 180px, 240px 240px, 170px 170px;
  }
  75% {
    mask-size: 80px 80px, 120px 120px, 100px 100px, 140px 140px, 90px 90px, 240px 240px, 170px 170px;
    -webkit-mask-size: 80px 80px, 120px 120px, 100px 100px, 140px 140px, 90px 90px, 240px 240px, 170px 170px;
  }
  90% {
    mask-size: 0 0, 60px 60px, 0 0, 70px 70px, 0 0, 120px 120px, 85px 85px;
    -webkit-mask-size: 0 0, 60px 60px, 0 0, 70px 70px, 0 0, 120px 120px, 85px 85px;
  }
  100% {
    mask-size: 0 0, 0 0, 0 0, 0 0, 0 0, 0 0, 0 0;
    -webkit-mask-size: 0 0, 0 0, 0 0, 0 0, 0 0, 0 0, 0 0;
  }
}

/* Organic fade patterns with edge constraints */
.container::after {
  content: '';
  position: absolute;
  top: -100px;
  left: -100px;
  right: -100px;
  bottom: -100px;
  background-image: 
    linear-gradient(#3c434b 1.2px, transparent 1.2px),
    linear-gradient(90deg, #3c434b 1.2px, transparent 1.2px);
  background-size: 48px 48px;
  opacity: 0.25;
  pointer-events: none;
  z-index: -1;
  
  /* Organic blob-like mask with edge constraints */
  mask: 
    /* Main edge vignette */
    radial-gradient(ellipse 110% 90% at center, black 40%, rgba(0,0,0,0.6) 60%, rgba(0,0,0,0.2) 80%, transparent 100%),
    /* Corner fades */
    linear-gradient(45deg, transparent 0%, rgba(0,0,0,0.4) 20%, black 35%, black 65%, rgba(0,0,0,0.4) 80%, transparent 100%),
    linear-gradient(-45deg, transparent 0%, rgba(0,0,0,0.4) 20%, black 35%, black 65%, rgba(0,0,0,0.4) 80%, transparent 100%),
    /* Organic blobs within constrained area */
    radial-gradient(ellipse 200px 150px at 20% 40%, transparent 30%, rgba(0,0,0,0.8) 80%),
    radial-gradient(ellipse 180px 120px at 80% 25%, transparent 25%, rgba(0,0,0,0.75) 75%),
    radial-gradient(ellipse 160px 200px at 30% 80%, transparent 40%, rgba(0,0,0,0.85) 85%),
    radial-gradient(ellipse 220px 140px at 70% 70%, transparent 35%, rgba(0,0,0,0.8) 80%),
    radial-gradient(ellipse 140px 180px at 90% 50%, transparent 20%, rgba(0,0,0,0.7) 70%);
  -webkit-mask: 
    radial-gradient(ellipse 110% 90% at center, black 40%, rgba(0,0,0,0.6) 60%, rgba(0,0,0,0.2) 80%, transparent 100%),
    linear-gradient(45deg, transparent 0%, rgba(0,0,0,0.4) 20%, black 35%, black 65%, rgba(0,0,0,0.4) 80%, transparent 100%),
    linear-gradient(-45deg, transparent 0%, rgba(0,0,0,0.4) 20%, black 35%, black 65%, rgba(0,0,0,0.4) 80%, transparent 100%),
    radial-gradient(ellipse 200px 150px at 20% 40%, transparent 30%, rgba(0,0,0,0.8) 80%),
    radial-gradient(ellipse 180px 120px at 80% 25%, transparent 25%, rgba(0,0,0,0.75) 75%),
    radial-gradient(ellipse 160px 200px at 30% 80%, transparent 40%, rgba(0,0,0,0.85) 85%),
    radial-gradient(ellipse 220px 140px at 70% 70%, transparent 35%, rgba(0,0,0,0.8) 80%),
    radial-gradient(ellipse 140px 180px at 90% 50%, transparent 20%, rgba(0,0,0,0.7) 70%);
  
  animation: organicFade 30s ease-in-out infinite;
}

@keyframes organicFade {
  0%, 100% {
    transform: rotate(0deg) scale(1);
    opacity: 0.3;
  }
  20% {
    transform: rotate(15deg) scale(1.1);
    opacity: 0.5;
  }
  40% {
    transform: rotate(-10deg) scale(0.9);
    opacity: 0.2;
  }
  60% {
    transform: rotate(25deg) scale(1.2);
    opacity: 0.6;
  }
  80% {
    transform: rotate(-5deg) scale(0.8);
    opacity: 0.4;
  }
}

/* Interactive grid response to component hover */
.hero-card:hover ~ .container::before {
  animation-duration: 15s;
  opacity: 0.9;
}

.hero-card:hover ~ .container::after {
  animation-duration: 20s;
  opacity: 0.5;
}

/* Enhanced grid visibility around component edges */
.hero-card::before {
  box-shadow: 
    0 0 100px rgba(255, 255, 255, 0.1),
    inset 0 0 50px rgba(255, 255, 255, 0.05);
}

/* Responsive grid adjustments */
@media (max-width: 768px) {
  body::before,
  body::after,
  .container::before,
  .container::after {
    background-size: 32px 32px;
  }
  
  .container::before {
    animation-duration: 20s;
  }
  
  .container::after {
    animation-duration: 25s;
  }
}

/* Performance optimization */
@media (prefers-reduced-motion: reduce) {
  body::after,
  .container::before,
  .container::after {
    animation: none !important;
  }
}