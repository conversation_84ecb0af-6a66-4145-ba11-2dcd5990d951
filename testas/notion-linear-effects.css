/* NOTION/LINEAR STILIUS - Minimalistiniai efektai */

/* Remove overwhelming effects */
.neural-network,
.morphing-shape,
.scroll-indicator,
.custom-cursor {
  display: none !important;
}

/* Subtle Linear-style interactions */
.hero-card {
  /* Remove complex animations */
  animation: none !important;
}

/* Clean button ripple effect like Linear */
.demo-button {
  position: relative;
  overflow: hidden;
}

.demo-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(94, 87, 255, 0.1);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
  pointer-events: none;
}

.demo-button:active::before {
  width: 100px;
  height: 100px;
}

/* Notion-style subtle glow on focus */
.demo-button:focus {
  outline: none;
  box-shadow: 
    var(--shadow-md),
    0 0 0 3px rgba(94, 87, 255, 0.1);
}

/* Linear-style smooth card entrance */
.hero-card {
  opacity: 1 !important;
  transform: translateY(0) !important;
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Notion-style content reveal */
.content > * {
  opacity: 0;
  transform: translateY(10px);
  animation: contentReveal 0.8s ease forwards;
}

.content .title {
  animation-delay: 0.1s;
}

.content .description {
  animation-delay: 0.2s;
}

.content .demo-button {
  animation-delay: 0.3s;
}

@keyframes contentReveal {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Linear-style floating elements - more subtle */
.floating-elements .floating-circle,
.floating-elements .floating-square {
  opacity: 0.3;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  backdrop-filter: none;
}

/* Notion-style micro-interactions */
.hero-card:hover .floating-circle,
.hero-card:hover .floating-square {
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

/* Linear-style typography refinements */
.title {
  /* Add subtle letter spacing like Linear */
  letter-spacing: -0.04em;
}

/* Clean focus states */
.demo-button:focus-visible {
  outline: 2px solid rgba(94, 87, 255, 0.5);
  outline-offset: 2px;
}

/* Notion-style loading state */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 100%
  );
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Linear-style reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .floating-circle,
  .floating-square {
    animation: none;
  }
}

/* Force light mode - disable dark mode */
@media (prefers-color-scheme: dark) {
  body {
    background: #fafbfc !important;
  }
  
  body::before,
  body::after {
    background-image: 
      linear-gradient(#c4c9cc 1.5px, transparent 1.5px),
      linear-gradient(90deg, #c4c9cc 1.5px, transparent 1.5px) !important;
    background-size: 48px 48px !important;
    opacity: 0.8 !important;
  }
}

/* Linear-style accessibility improvements */
.hero-card:focus-within {
  outline: 2px solid rgba(94, 87, 255, 0.3);
  outline-offset: 4px;
}

/* Notion-style smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Linear-style selection */
::selection {
  background: rgba(94, 87, 255, 0.2);
  color: inherit;
}

/* Clean print styles */
@media print {
  .floating-elements,
  .demo-button {
    display: none;
  }
  
  .hero-card {
    box-shadow: none;
    border: 1px solid var(--notion-gray-300);
  }
}