/* Notion/Linear Design System */
:root {
  /* Notion color palette */
  --notion-gray-100: #f7f6f3;
  --notion-gray-200: #e9e5e1;
  --notion-gray-300: #d0c8c1;
  --notion-gray-400: #a39d97;
  --notion-gray-500: #6b6660;
  --notion-gray-600: #514e48;
  --notion-gray-700: #37352f;
  --notion-gray-800: #2f3437;
  --notion-gray-900: #191919;
  
  /* Linear color palette */
  --linear-purple: #5e57ff;
  --linear-purple-light: #8b85ff;
  --linear-blue: #0969da;
  --linear-gray-100: #fafbfc;
  --linear-gray-200: #f1f3f4;
  --linear-gray-300: #e1e4e8;
  --linear-gray-400: #c4c9cc;
  --linear-gray-500: #8b949e;
  --linear-gray-600: #656d76;
  --linear-gray-700: #3c434b;
  --linear-gray-800: #22262a;
  --linear-gray-900: #1c2024;
  
  /* Design tokens */
  --border-radius: 12px;
  --border-radius-lg: 16px;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Typography */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, 'Apple Color Emoji', Arial, sans-serif, 'Segoe UI Emoji', 'Segoe UI Symbol';
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  background: #fafbfc !important;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
  line-height: 1.6;
}

/* Enhanced grid pattern with smooth fade to edges */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(#c4c9cc 1.5px, transparent 1.5px),
    linear-gradient(90deg, #c4c9cc 1.5px, transparent 1.5px);
  background-size: 48px 48px;
  opacity: 1;
  pointer-events: none;
  z-index: -1;
  
  /* Complex mask for smooth edge fade-out */
  mask: 
    /* Center focus area */
    radial-gradient(
      ellipse 900px 700px at center,
      transparent 15%,
      rgba(0, 0, 0, 0.2) 25%,
      rgba(0, 0, 0, 0.4) 35%,
      rgba(0, 0, 0, 0.6) 45%,
      rgba(0, 0, 0, 0.8) 55%,
      rgba(0, 0, 0, 1) 65%,
      rgba(0, 0, 0, 0.8) 75%,
      rgba(0, 0, 0, 0.6) 85%,
      rgba(0, 0, 0, 0.3) 95%,
      transparent 100%
    ),
    /* Edge fade gradients */
    linear-gradient(to right, transparent 0%, black 15%, black 85%, transparent 100%),
    linear-gradient(to bottom, transparent 0%, black 15%, black 85%, transparent 100%);
  -webkit-mask: 
    radial-gradient(
      ellipse 900px 700px at center,
      transparent 15%,
      rgba(0, 0, 0, 0.2) 25%,
      rgba(0, 0, 0, 0.4) 35%,
      rgba(0, 0, 0, 0.6) 45%,
      rgba(0, 0, 0, 0.8) 55%,
      rgba(0, 0, 0, 1) 65%,
      rgba(0, 0, 0, 0.8) 75%,
      rgba(0, 0, 0, 0.6) 85%,
      rgba(0, 0, 0, 0.3) 95%,
      transparent 100%
    ),
    linear-gradient(to right, transparent 0%, black 15%, black 85%, transparent 100%),
    linear-gradient(to bottom, transparent 0%, black 15%, black 85%, transparent 100%);
}

.container {
  width: 100%;
  max-width: 1200px;
  padding: 2rem;
  perspective: 1000px;
}

.hero-card {
  position: relative;
  background: linear-gradient(
    135deg, 
    #667eea 0%, 
    #764ba2 15%, 
    #f093fb 30%, 
    #f5576c 45%, 
    #4facfe 60%, 
    #00f2fe 75%, 
    #43e97b 90%, 
    #38f9d7 100%
  );
  border-radius: 16px;
  padding: 4rem 3rem;
  text-align: center;
  color: white;
  overflow: hidden;
  
  /* Enhanced shadows */
  box-shadow: 
    0 20px 40px -5px rgba(0, 0, 0, 0.15), 
    0 10px 20px -5px rgba(102, 126, 234, 0.2);
  transform: translateY(0px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Gradient border */
  border: 1px solid rgba(255, 255, 255, 0.2);
  
  /* Smooth gradient animation */
  background-size: 300% 300%;
  animation: gradientFlow 8s ease infinite;
  
  /* Ensure visibility */
  opacity: 1;
  z-index: 1;
}

@keyframes gradientFlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.hero-card:hover {
  transform: translateY(-8px) scale(1.01);
  box-shadow: 
    0 40px 80px -12px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    0 0 60px rgba(102, 126, 234, 0.4);
  animation-duration: 4s;
}

.hero-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, transparent 40%, rgba(255, 255, 255, 0.05) 100%),
    radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.2) 0%, transparent 60%);
  pointer-events: none;
  border-radius: 16px;
  opacity: 0.8;
}

/* Moving shadow element inside component */
.hero-card::after {
  content: '';
  position: absolute;
  top: 20%;
  left: -50%;
  width: 200%;
  height: 60%;
  background: radial-gradient(
    ellipse 400px 200px at center,
    rgba(0, 0, 0, 0.15) 0%,
    rgba(0, 0, 0, 0.08) 30%,
    transparent 70%
  );
  border-radius: 50%;
  opacity: 0.6;
  pointer-events: none;
  z-index: 1;
  animation: movingShadow 12s ease-in-out infinite;
  transform-origin: center;
}

@keyframes movingShadow {
  0% {
    left: -50%;
    top: 20%;
    transform: scale(0.8) rotate(0deg);
    opacity: 0.3;
  }
  25% {
    left: 10%;
    top: 10%;
    transform: scale(1.2) rotate(90deg);
    opacity: 0.6;
  }
  50% {
    left: 30%;
    top: 30%;
    transform: scale(1.5) rotate(180deg);
    opacity: 0.8;
  }
  75% {
    left: 20%;
    top: 50%;
    transform: scale(1.1) rotate(270deg);
    opacity: 0.5;
  }
  100% {
    left: -50%;
    top: 20%;
    transform: scale(0.8) rotate(360deg);
    opacity: 0.3;
  }
}

.content {
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin: 0 auto;
}

.title {
  font-size: clamp(2rem, 4vw, 3.5rem);
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  letter-spacing: -0.025em;
  color: white;
  
  /* Clean typography like Linear */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.description {
  font-size: 1.125rem;
  line-height: 1.6;
  margin-bottom: 3rem;
  opacity: 0.9;
  font-weight: 400;
  max-width: 650px;
  margin-left: auto;
  margin-right: auto;
  color: rgba(255, 255, 255, 0.95);
}

.demo-button {
  background: rgba(255, 255, 255, 1);
  color: var(--linear-purple);
  border: none;
  padding: 0.875rem 2rem;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  font-family: var(--font-family);
  
  /* Linear/Notion button style */
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.demo-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
  background: rgba(255, 255, 255, 1);
}

.demo-button:active {
  transform: translateY(0px);
  box-shadow: var(--shadow-sm);
}

/* Minimalist floating elements */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-circle,
.floating-square {
  position: absolute;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.floating-square {
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.03);
}

.circle-1 {
  width: 60px;
  height: 60px;
  top: 15%;
  left: 10%;
  animation: subtleFloat1 8s ease-in-out infinite;
}

.circle-2 {
  width: 40px;
  height: 40px;
  top: 20%;
  right: 15%;
  animation: subtleFloat2 10s ease-in-out infinite;
}

.circle-3 {
  width: 80px;
  height: 80px;
  bottom: 20%;
  left: 5%;
  animation: subtleFloat3 9s ease-in-out infinite;
}

.square-1 {
  width: 32px;
  height: 32px;
  top: 60%;
  right: 10%;
  animation: subtleFloat4 12s ease-in-out infinite;
}

.square-2 {
  width: 48px;
  height: 48px;
  bottom: 15%;
  right: 20%;
  animation: subtleFloat5 7s ease-in-out infinite;
}

/* Subtle floating animations like Linear */
@keyframes subtleFloat1 {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-8px); }
}

@keyframes subtleFloat2 {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-12px); }
}

@keyframes subtleFloat3 {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-6px); }
}

@keyframes subtleFloat4 {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes subtleFloat5 {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-14px); }
}

/* Responsive design */
@media (max-width: 768px) {
  .hero-card {
    padding: 3rem 2rem;
    margin: 1rem;
  }
  
  .title {
    font-size: 2.5rem;
  }
  
  .description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }
  
  .demo-button {
    padding: 0.875rem 2rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .floating-circle,
  .floating-square {
    opacity: 0.5;
  }
  
  .circle-1, .circle-2, .circle-3 {
    width: 40px;
    height: 40px;
  }
  
  .square-1, .square-2 {
    width: 30px;
    height: 30px;
  }
}