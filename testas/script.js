// Modernūs JavaScript efektai

class FloatingAnimation {
    constructor() {
        this.init();
        this.addInteractivity();
        this.addParallaxEffect();
    }

    init() {
        // Smooth scroll behavior
        document.documentElement.style.scrollBehavior = 'smooth';
        
        // Pridėti hover efektus
        this.addHoverEffects();
        
        // Pridėti pelės sekimo efektą
        this.addMouseTracking();
    }

    addHoverEffects() {
        const heroCard = document.querySelector('.hero-card');
        const floatingElements = document.querySelectorAll('.floating-circle, .floating-square');

        heroCard.addEventListener('mouseenter', () => {
            floatingElements.forEach((element, index) => {
                element.style.animationDuration = `${Math.random() * 3 + 2}s`;
                element.style.animationDelay = `${index * 0.2}s`;
            });
        });

        heroCard.addEventListener('mouseleave', () => {
            floatingElements.forEach(element => {
                element.style.animationDuration = '';
                element.style.animationDelay = '';
            });
        });
    }

    addInteractivity() {
        const button = document.querySelector('.demo-button');
        
        // Pridėti ripple efektą mygtukui
        button.addEventListener('click', (e) => {
            this.createRipple(e, button);
            
            // Simuluoti veiksmą
            setTimeout(() => {
                this.showNotification('Demonstracijos užklausa išsiųsta!');
            }, 300);
        });

        // Pridėti hover sound efektą (vizualus)
        button.addEventListener('mouseenter', () => {
            button.style.filter = 'brightness(1.1)';
        });

        button.addEventListener('mouseleave', () => {
            button.style.filter = '';
        });
    }

    createRipple(event, element) {
        const circle = document.createElement('span');
        const diameter = Math.max(element.clientWidth, element.clientHeight);
        const radius = diameter / 2;

        circle.style.width = circle.style.height = `${diameter}px`;
        circle.style.left = `${event.clientX - element.offsetLeft - radius}px`;
        circle.style.top = `${event.clientY - element.offsetTop - radius}px`;
        circle.style.position = 'absolute';
        circle.style.borderRadius = '50%';
        circle.style.background = 'rgba(255, 255, 255, 0.3)';
        circle.style.transform = 'scale(0)';
        circle.style.animation = 'ripple 0.6s linear';
        circle.style.pointerEvents = 'none';

        element.appendChild(circle);

        setTimeout(() => {
            circle.remove();
        }, 600);
    }

    addMouseTracking() {
        const heroCard = document.querySelector('.hero-card');
        
        document.addEventListener('mousemove', (e) => {
            const rect = heroCard.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            const rotateX = (y - centerY) / 20;
            const rotateY = (centerX - x) / 20;
            
            if (x >= 0 && x <= rect.width && y >= 0 && y <= rect.height) {
                heroCard.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(0px)`;
            } else {
                heroCard.style.transform = '';
            }
        });

        document.addEventListener('mouseleave', () => {
            heroCard.style.transform = '';
        });
    }

    addParallaxEffect() {
        const floatingElements = document.querySelectorAll('.floating-circle, .floating-square');
        
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            
            floatingElements.forEach((element, index) => {
                const speed = 0.5 + (index * 0.1);
                const yPos = -(scrolled * speed);
                element.style.transform += ` translateY(${yPos}px)`;
            });
        });
    }

    showNotification(message) {
        // Sukurti notification elementą
        const notification = document.createElement('div');
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
            z-index: 1000;
            font-weight: 500;
            transform: translateX(100%);
            transition: transform 0.3s cubic-bezier(0.23, 1, 0.320, 1);
        `;

        document.body.appendChild(notification);

        // Animacija
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Pašalinti po 3 sekundžių
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

// Pridėti ripple animacijos CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .demo-button {
        position: relative;
        overflow: hidden;
    }
`;
document.head.appendChild(style);

// Inicijuoti animacijas kai puslapis užkrautas
document.addEventListener('DOMContentLoaded', () => {
    new FloatingAnimation();
    
    // Pridėti loading animaciją
    const heroCard = document.querySelector('.hero-card');
    heroCard.style.opacity = '0';
    heroCard.style.transform = 'translateY(50px)';
    
    setTimeout(() => {
        heroCard.style.transition = 'all 0.8s cubic-bezier(0.23, 1, 0.320, 1)';
        heroCard.style.opacity = '1';
        heroCard.style.transform = 'translateY(0)';
    }, 200);
});

// Performance optimization
if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
        // Pridėti papildomus efektus
        console.log('Modernūs efektai užkrauti');
    });
}