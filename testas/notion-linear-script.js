// NOTION/LINEAR STILIUS - Minimalistiniai JavaScript efektai

class NotionLinearEffects {
    constructor() {
        this.init();
        this.initCleanInteractions();
        this.initAccessibility();
    }

    init() {
        // Smooth performance
        document.documentElement.style.scrollBehavior = 'smooth';
        
        // Clean entrance animation
        this.initEntranceAnimation();
        
        // Subtle interactions
        this.initSubtleEffects();
    }

    initEntranceAnimation() {
        const heroCard = document.querySelector('.hero-card');
        
        // Intersection Observer for entrance
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animationPlayState = 'running';
                }
            });
        }, { threshold: 0.2 });

        observer.observe(heroCard);
    }

    initCleanInteractions() {
        const button = document.querySelector('.demo-button');
        
        // Clean ripple effect
        button.addEventListener('click', (e) => {
            this.createCleanRipple(e, button);
            this.showNotification('Demonstracijos užklausa išsiųsta');
        });

        // Notion-style hover effects
        button.addEventListener('mouseenter', () => {
            button.style.transform = 'translateY(-1px)';
        });

        button.addEventListener('mouseleave', () => {
            button.style.transform = '';
        });
    }

    createCleanRipple(event, element) {
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        const ripple = document.createElement('div');
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(94, 87, 255, 0.1);
            border-radius: 50%;
            transform: scale(0);
            animation: cleanRipple 0.4s ease-out;
            pointer-events: none;
            z-index: 10;
        `;

        element.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 400);
    }

    initSubtleEffects() {
        const heroCard = document.querySelector('.hero-card');
        
        // Subtle mouse tracking like Linear
        heroCard.addEventListener('mousemove', (e) => {
            if (window.innerWidth > 768) { // Only on desktop
                const rect = heroCard.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;
                
                const rotateX = (y - centerY) / 40;
                const rotateY = (centerX - x) / 40;
                
                heroCard.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
            }
        });

        heroCard.addEventListener('mouseleave', () => {
            heroCard.style.transform = '';
        });

        // Floating elements interaction
        const floatingElements = document.querySelectorAll('.floating-circle, .floating-square');
        
        heroCard.addEventListener('mouseenter', () => {
            floatingElements.forEach(element => {
                element.style.opacity = '0.5';
                element.style.transform = 'translateY(-4px)';
            });
        });

        heroCard.addEventListener('mouseleave', () => {
            floatingElements.forEach(element => {
                element.style.opacity = '';
                element.style.transform = '';
            });
        });
    }

    initAccessibility() {
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-nav');
            }
        });

        document.addEventListener('mousedown', () => {
            document.body.classList.remove('keyboard-nav');
        });

        // Focus management
        const focusableElements = document.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
        
        focusableElements.forEach(element => {
            element.addEventListener('focus', () => {
                element.style.outline = '2px solid rgba(94, 87, 255, 0.5)';
                element.style.outlineOffset = '2px';
            });

            element.addEventListener('blur', () => {
                element.style.outline = '';
                element.style.outlineOffset = '';
            });
        });
    }

    showNotification(message) {
        // Check if notification already exists
        const existingNotification = document.querySelector('.notion-notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        const notification = document.createElement('div');
        notification.className = 'notion-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 24px;
            right: 24px;
            background: white;
            color: var(--notion-gray-700);
            padding: 12px 16px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            z-index: 10000;
            font-size: 14px;
            font-weight: 500;
            border: 1px solid var(--notion-gray-200);
            transform: translateX(100%);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-family: var(--font-family);
        `;

        document.body.appendChild(notification);

        // Entrance animation
        requestAnimationFrame(() => {
            notification.style.transform = 'translateX(0)';
        });

        // Auto remove
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }

    // Utility method for clean state management
    static addGlobalStyles() {
        const styles = document.createElement('style');
        styles.textContent = `
            @keyframes cleanRipple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
            
            .keyboard-nav *:focus {
                outline: 2px solid rgba(94, 87, 255, 0.5) !important;
                outline-offset: 2px !important;
            }
            
            /* Smooth transitions for all interactive elements */
            .hero-card,
            .demo-button,
            .floating-circle,
            .floating-square {
                transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            }
            
            /* Reduced motion support */
            @media (prefers-reduced-motion: reduce) {
                .hero-card,
                .demo-button,
                .floating-circle,
                .floating-square {
                    transition: none !important;
                    animation: none !important;
                }
            }
            
            /* High contrast mode support */
            @media (prefers-contrast: high) {
                .hero-card {
                    border: 2px solid currentColor;
                }
                
                .demo-button {
                    border: 2px solid currentColor;
                }
            }
        `;
        
        document.head.appendChild(styles);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    NotionLinearEffects.addGlobalStyles();
    new NotionLinearEffects();
    
    console.log('✨ Notion/Linear stilius aktyvuotas');
});

// Performance monitoring
if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
        // Lazy load additional optimizations
        console.log('🚀 Performance optimizacijos užkrautos');
    });
}

// Export for external use
window.NotionLinearEffects = NotionLinearEffects;