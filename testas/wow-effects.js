// WOW EFEKTAI - Ultra modernūs JavaScript efektai

class WowEffects {
    constructor() {
        this.init();
        this.createNeuralNetwork();
        this.initCustomCursor();
        this.initParticleSystem();
        this.initAdvancedInteractions();
    }

    init() {
        // Smooth performance optimizations
        document.body.style.willChange = 'transform';
        
        // Initialize intersection observer for animations
        this.setupIntersectionObserver();
    }

    createNeuralNetwork() {
        const neuralNetwork = document.querySelector('.neural-network');
        const nodeCount = 15;
        const connectionCount = 20;

        // Create nodes
        for (let i = 0; i < nodeCount; i++) {
            const node = document.createElement('div');
            node.className = 'neural-node';
            node.style.left = `${Math.random() * 100}%`;
            node.style.top = `${Math.random() * 100}%`;
            node.style.animationDelay = `${Math.random() * 2}s`;
            neuralNetwork.appendChild(node);
        }

        // Create connections
        for (let i = 0; i < connectionCount; i++) {
            const connection = document.createElement('div');
            connection.className = 'neural-connection';
            
            const startX = Math.random() * 100;
            const startY = Math.random() * 100;
            const endX = Math.random() * 100;
            const endY = Math.random() * 100;
            
            const length = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2));
            const angle = Math.atan2(endY - startY, endX - startX) * 180 / Math.PI;
            
            connection.style.left = `${startX}%`;
            connection.style.top = `${startY}%`;
            connection.style.width = `${length * 5}px`;
            connection.style.transform = `rotate(${angle}deg)`;
            connection.style.animationDelay = `${Math.random() * 3}s`;
            
            neuralNetwork.appendChild(connection);
        }
    }

    initCustomCursor() {
        const cursor = document.querySelector('.custom-cursor');
        const heroCard = document.querySelector('.hero-card');

        document.addEventListener('mousemove', (e) => {
            cursor.style.left = e.clientX + 'px';
            cursor.style.top = e.clientY + 'px';
        });

        // Magnetic effect
        heroCard.addEventListener('mousemove', (e) => {
            const rect = heroCard.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            const deltaX = (x - centerX) * 0.15;
            const deltaY = (y - centerY) * 0.15;
            
            heroCard.style.transform = `perspective(1000px) rotateY(${deltaX / 10}deg) rotateX(${-deltaY / 10}deg) translateZ(10px)`;
        });

        heroCard.addEventListener('mouseleave', () => {
            heroCard.style.transform = '';
        });

        // Cursor morphing
        const interactiveElements = document.querySelectorAll('.demo-button, .title');
        interactiveElements.forEach(element => {
            element.addEventListener('mouseenter', () => {
                cursor.style.transform = 'scale(2)';
                cursor.style.background = 'radial-gradient(circle, #60a5fa 0%, transparent 50%)';
            });

            element.addEventListener('mouseleave', () => {
                cursor.style.transform = 'scale(1)';
                cursor.style.background = 'radial-gradient(circle, #3b82f6 0%, transparent 70%)';
            });
        });
    }

    initParticleSystem() {
        const button = document.querySelector('.demo-button');
        
        button.addEventListener('click', (e) => {
            this.createParticleExplosion(e.clientX, e.clientY);
            this.createRippleWave(e);
        });
    }

    createParticleExplosion(x, y) {
        const particleCount = 20;
        const explosionContainer = document.createElement('div');
        explosionContainer.className = 'particle-explosion';
        explosionContainer.style.left = x + 'px';
        explosionContainer.style.top = y + 'px';
        
        document.body.appendChild(explosionContainer);

        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            const angle = (360 / particleCount) * i;
            const distance = 50 + Math.random() * 100;
            const randomX = Math.cos(angle * Math.PI / 180) * distance;
            const randomY = Math.sin(angle * Math.PI / 180) * distance;
            
            particle.style.setProperty('--random-x', randomX + 'px');
            particle.style.setProperty('--random-y', randomY + 'px');
            particle.style.animationDelay = Math.random() * 0.1 + 's';
            
            explosionContainer.appendChild(particle);
        }

        setTimeout(() => {
            document.body.removeChild(explosionContainer);
        }, 1000);
    }

    createRippleWave(event) {
        const button = event.currentTarget;
        const rect = button.getBoundingClientRect();
        
        const ripple = document.createElement('div');
        ripple.style.cssText = `
            position: absolute;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.6) 0%, transparent 70%);
            transform: scale(0);
            animation: megaRipple 0.8s ease-out;
            pointer-events: none;
            z-index: 1000;
        `;
        
        const size = Math.max(rect.width, rect.height) * 2;
        ripple.style.width = size + 'px';
        ripple.style.height = size + 'px';
        ripple.style.left = (event.clientX - rect.left - size / 2) + 'px';
        ripple.style.top = (event.clientY - rect.top - size / 2) + 'px';
        
        button.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 800);
    }

    initAdvancedInteractions() {
        // Voice activation simulation
        this.initVoiceEffects();
        
        // Gesture recognition simulation
        this.initGestureEffects();
        
        // Eye tracking simulation
        this.initEyeTrackingEffect();
        
        // Performance monitoring
        this.initPerformanceMonitoring();
    }

    initVoiceEffects() {
        let isListening = false;
        
        document.addEventListener('keydown', (e) => {
            if (e.key === ' ' && !isListening) {
                isListening = true;
                this.createVoiceWave();
            }
        });

        document.addEventListener('keyup', (e) => {
            if (e.key === ' ') {
                isListening = false;
            }
        });
    }

    createVoiceWave() {
        const heroCard = document.querySelector('.hero-card');
        heroCard.style.animation = 'voicePulse 0.5s ease-in-out';
        
        setTimeout(() => {
            heroCard.style.animation = '';
        }, 500);
    }

    initGestureEffects() {
        let startX, startY;
        
        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });

        document.addEventListener('touchend', (e) => {
            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            
            const deltaX = endX - startX;
            const deltaY = endY - startY;
            
            if (Math.abs(deltaX) > 100 || Math.abs(deltaY) > 100) {
                this.createGestureEffect(deltaX, deltaY);
            }
        });
    }

    createGestureEffect(deltaX, deltaY) {
        const heroCard = document.querySelector('.hero-card');
        const direction = Math.abs(deltaX) > Math.abs(deltaY) ? 'horizontal' : 'vertical';
        
        heroCard.style.transform = `
            perspective(1000px) 
            rotate${direction === 'horizontal' ? 'Y' : 'X'}(${deltaX / 10}deg) 
            scale(1.05)
        `;
        
        setTimeout(() => {
            heroCard.style.transform = '';
        }, 300);
    }

    initEyeTrackingEffect() {
        // Simulate eye tracking with mouse movement
        let eyeTrackingActive = false;
        
        document.addEventListener('dblclick', () => {
            eyeTrackingActive = !eyeTrackingActive;
            this.showNotification(eyeTrackingActive ? 'Eye tracking aktyvuotas' : 'Eye tracking išjungtas');
        });

        document.addEventListener('mousemove', (e) => {
            if (eyeTrackingActive) {
                this.updateEyeTracking(e.clientX, e.clientY);
            }
        });
    }

    updateEyeTracking(x, y) {
        const centerX = window.innerWidth / 2;
        const centerY = window.innerHeight / 2;
        
        const deltaX = (x - centerX) / centerX;
        const deltaY = (y - centerY) / centerY;
        
        const heroCard = document.querySelector('.hero-card');
        heroCard.style.filter = `hue-rotate(${deltaX * 30}deg) brightness(${1 + deltaY * 0.2})`;
    }

    initPerformanceMonitoring() {
        let frameCount = 0;
        let lastTime = performance.now();
        
        const monitor = () => {
            frameCount++;
            const currentTime = performance.now();
            
            if (currentTime - lastTime >= 1000) {
                const fps = Math.round(frameCount * 1000 / (currentTime - lastTime));
                
                // Optimize based on FPS
                if (fps < 30) {
                    this.optimizePerformance();
                }
                
                frameCount = 0;
                lastTime = currentTime;
            }
            
            requestAnimationFrame(monitor);
        };
        
        monitor();
    }

    optimizePerformance() {
        // Reduce animation complexity on low FPS
        const complexAnimations = document.querySelectorAll('.neural-node, .neural-connection');
        complexAnimations.forEach(element => {
            element.style.animationDuration = '4s'; // Slower animations
        });
    }

    setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animationPlayState = 'running';
                } else {
                    entry.target.style.animationPlayState = 'paused';
                }
            });
        });

        // Observe animated elements
        const animatedElements = document.querySelectorAll('[class*="float"], [class*="morph"], [class*="neural"]');
        animatedElements.forEach(element => observer.observe(element));
    }

    showNotification(message) {
        const notification = document.createElement('div');
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(30, 64, 175, 0.4);
            z-index: 10000;
            font-weight: 600;
            transform: translateX(100%);
            transition: all 0.5s cubic-bezier(0.23, 1, 0.320, 1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 500);
        }, 3000);
    }
}

// Additional CSS animations
const wowAnimations = document.createElement('style');
wowAnimations.textContent = `
    @keyframes megaRipple {
        to {
            transform: scale(2);
            opacity: 0;
        }
    }
    
    @keyframes voicePulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); box-shadow: 0 0 100px rgba(30, 64, 175, 0.8); }
    }
    
    .hero-card {
        will-change: transform, filter;
    }
    
    /* GPU acceleration */
    .floating-circle, .floating-square, .morphing-shape {
        will-change: transform;
        backface-visibility: hidden;
    }
`;
document.head.appendChild(wowAnimations);

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new WowEffects();
    
    console.log('🚀 WOW Effects aktivuoti! Dvigubas click = eye tracking');
});

// Export for potential external use
window.WowEffects = WowEffects;