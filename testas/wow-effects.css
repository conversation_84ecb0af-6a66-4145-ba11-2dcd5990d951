/* WOW EFEKTAI - Ultra modernūs vizualiniai efektai */

/* Holographic border efektas */
.hero-card::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: conic-gradient(
    from 0deg,
    #1e40af 0deg,
    #3b82f6 60deg,
    #60a5fa 120deg,
    #93c5fd 180deg,
    #dbeafe 240deg,
    #3b82f6 300deg,
    #1e40af 360deg
  );
  border-radius: calc(var(--border-radius) + 2px);
  z-index: -1;
  opacity: 0;
  animation: holographicBorder 4s linear infinite;
  transition: opacity 0.5s ease;
}

.hero-card:hover::after {
  opacity: 0.7;
}

@keyframes holographicBorder {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Liquid loading effect */
.demo-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(30, 64, 175, 0.4) 50%,
    transparent 100%
  );
  transition: left 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-radius: 50px;
}

.demo-button:hover::after {
  left: 100%;
}

/* Particle explosion effect */
.particle-explosion {
  position: absolute;
  pointer-events: none;
  z-index: 1000;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, #60a5fa 0%, #3b82f6 50%, transparent 100%);
  border-radius: 50%;
  animation: explode 0.8s ease-out forwards;
}

@keyframes explode {
  0% {
    transform: translate(0, 0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(var(--random-x, 0), var(--random-y, 0)) scale(0);
    opacity: 0;
  }
}

/* Neural network background */
.neural-network {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -2;
}

.neural-node {
  position: absolute;
  width: 3px;
  height: 3px;
  background: radial-gradient(circle, #3b82f6 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.neural-connection {
  position: absolute;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #1e40af 50%, transparent 100%);
  animation: connectionFlow 3s linear infinite;
  opacity: 0.3;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.6; }
  50% { transform: scale(1.5); opacity: 1; }
}

@keyframes connectionFlow {
  0% { transform: scaleX(0); }
  50% { transform: scaleX(1); }
  100% { transform: scaleX(0); }
}

/* Morphing background shapes */
.morphing-shape {
  position: absolute;
  background: linear-gradient(45deg, #1e40af, #3b82f6);
  border-radius: 50%;
  opacity: 0.1;
  animation: morph 8s ease-in-out infinite;
}

.morphing-shape:nth-child(1) {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.morphing-shape:nth-child(2) {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.morphing-shape:nth-child(3) {
  width: 180px;
  height: 180px;
  bottom: 10%;
  left: 50%;
  animation-delay: 4s;
}

@keyframes morph {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    border-radius: 50%;
  }
  25% {
    transform: scale(1.2) rotate(90deg);
    border-radius: 30%;
  }
  50% {
    transform: scale(0.8) rotate(180deg);
    border-radius: 10%;
  }
  75% {
    transform: scale(1.1) rotate(270deg);
    border-radius: 40%;
  }
}

/* Text glow effect */
.title {
  position: relative;
  text-shadow: 
    0 0 10px rgba(255, 255, 255, 0.5),
    0 0 20px rgba(59, 130, 246, 0.3),
    0 0 30px rgba(30, 64, 175, 0.2);
}

.title::before {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(
    45deg,
    #60a5fa 0%,
    #93c5fd 25%,
    #dbeafe 50%,
    #93c5fd 75%,
    #60a5fa 100%
  );
  background-size: 200% 200%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: textShine 3s ease-in-out infinite;
  z-index: -1;
}

@keyframes textShine {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Ultra modern scroll indicator */
.scroll-indicator {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 50px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  opacity: 0.7;
  z-index: 100;
}

.scroll-indicator::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 8px;
  background: white;
  border-radius: 2px;
  animation: scrollDown 2s ease-in-out infinite;
}

@keyframes scrollDown {
  0% { opacity: 0; transform: translateX(-50%) translateY(0); }
  50% { opacity: 1; }
  100% { opacity: 0; transform: translateX(-50%) translateY(20px); }
}

/* Enhanced floating elements */
.floating-elements .floating-circle,
.floating-elements .floating-square {
  background: linear-gradient(45deg, 
    rgba(30, 64, 175, 0.2) 0%, 
    rgba(59, 130, 246, 0.1) 50%, 
    rgba(147, 197, 253, 0.2) 100%);
  box-shadow: 
    0 8px 32px rgba(30, 64, 175, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Magnetic cursor effect */
.hero-card {
  cursor: none;
}

.custom-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  background: radial-gradient(circle, #3b82f6 0%, transparent 70%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  transition: transform 0.1s ease;
  mix-blend-mode: difference;
}

.custom-cursor::after {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  width: 40px;
  height: 40px;
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 50%;
  animation: cursorPulse 1.5s ease-in-out infinite;
}

@keyframes cursorPulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.5; }
}